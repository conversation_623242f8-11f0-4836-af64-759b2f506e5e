<template>
	<view class="test-container">
		<custom-nav-bar title="API测试"></custom-nav-bar>
		
		<view class="test-content">
			<view class="test-section">
				<view class="section-title">用户信息</view>
				<view class="info-item">
					<text>Token: {{ token ? '已登录' : '未登录' }}</text>
				</view>
				<view class="info-item" v-if="userInfo">
					<text>用户名: {{ userInfo.wxNickname || '未设置' }}</text>
				</view>
				<view class="info-item" v-if="userInfo">
					<text>手机号: {{ userInfo.phoneNumber || '未设置' }}</text>
				</view>
			</view>

			<view class="test-section">
				<view class="section-title">API测试</view>
				
				<button class="test-btn" @click="testTastingReservation">测试试餐预约</button>
				<button class="test-btn" @click="testRoomViewingReservation">测试看房预约</button>
				<button class="test-btn" @click="testCarReservation">测试用车预约</button>
				<button class="test-btn" @click="testHandicraftClassList">测试手工课列表</button>
				<button class="test-btn" @click="testSupplyRequest">测试用品补充需求</button>
			</view>

			<view class="test-section" v-if="testResult">
				<view class="section-title">测试结果</view>
				<view class="result-content">{{ testResult }}</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { createTastingReservation, createRoomViewingReservation, createCarReservation } from '@/config/api/reservation.js';
import { getHandicraftClassList } from '@/config/api/handicraft.js';
import { submitSupplyRequest } from '@/config/api/supply.js';

const token = ref('');
const userInfo = ref(null);
const testResult = ref('');

// 获取用户信息
const getUserInfo = () => {
	try {
		token.value = uni.getStorageSync('token');
		const userInfoStr = uni.getStorageSync('userInfo');
		if (userInfoStr) {
			userInfo.value = JSON.parse(userInfoStr);
		}
	} catch (error) {
		console.error('获取用户信息失败:', error);
	}
};

// 测试试餐预约
const testTastingReservation = async () => {
	try {
		testResult.value = '正在测试试餐预约...';
		
		const testData = {
			contactPhone: '13800138000',
			userName: '测试用户',
			reservationTime: '2024-08-01 12:00:00',
			description: '试餐预约测试 - 中餐 - 2人',
			extendedProperties: JSON.stringify({
				mealTime: '中餐',
				specificTime: '12:00',
				participantCount: 2,
				dietaryPreference: '无特殊要求',
				remarks: '测试数据'
			})
		};

		const result = await createTastingReservation(testData);
		testResult.value = `试餐预约测试成功: ${JSON.stringify(result, null, 2)}`;
	} catch (error) {
		testResult.value = `试餐预约测试失败: ${error.message}`;
		console.error('试餐预约测试失败:', error);
	}
};

// 测试看房预约
const testRoomViewingReservation = async () => {
	try {
		testResult.value = '正在测试看房预约...';
		
		const testData = {
			contactPhone: '13800138000',
			userName: '测试用户',
			reservationTime: '2024-08-01 15:00:00',
			description: '看房预约测试',
			extendedProperties: JSON.stringify({
				participantCount: 2,
				roomType: '标准间',
				specialRequirements: '无特殊要求',
				remarks: '测试数据'
			})
		};

		const result = await createRoomViewingReservation(testData);
		testResult.value = `看房预约测试成功: ${JSON.stringify(result, null, 2)}`;
	} catch (error) {
		testResult.value = `看房预约测试失败: ${error.message}`;
		console.error('看房预约测试失败:', error);
	}
};

// 测试用车预约
const testCarReservation = async () => {
	try {
		testResult.value = '正在测试用车预约...';
		
		const testData = {
			contactPhone: '13800138000',
			userName: '测试用户',
			reservationTime: '2024-08-01 16:00:00',
			description: '用车预约测试',
			extendedProperties: JSON.stringify({
				carType: '轿车',
				passengerCount: 2,
				startLocation: '月子中心',
				destination: '医院',
				needWaiting: false,
				remarks: '测试数据'
			}),
			reservationType: 2,
			title: '用车预约'
		};

		const result = await createCarReservation(testData);
		testResult.value = `用车预约测试成功: ${JSON.stringify(result, null, 2)}`;
	} catch (error) {
		testResult.value = `用车预约测试失败: ${error.message}`;
		console.error('用车预约测试失败:', error);
	}
};

// 测试手工课列表
const testHandicraftClassList = async () => {
	try {
		testResult.value = '正在测试手工课列表...';
		
		const testData = {
			pageNum: 1,
			pageSize: 10
		};

		const result = await getHandicraftClassList(testData);
		testResult.value = `手工课列表测试成功: ${JSON.stringify(result, null, 2)}`;
	} catch (error) {
		testResult.value = `手工课列表测试失败: ${error.message}`;
		console.error('手工课列表测试失败:', error);
	}
};

// 测试用品补充需求
const testSupplyRequest = async () => {
	try {
		testResult.value = '正在测试用品补充需求...';
		
		const testData = {
			itemName: '测试用品',
			category: '生活用品',
			quantity: 1,
			unit: '个',
			urgency: 1,
			description: '测试用品补充需求',
			contactPhone: '13800138000'
		};

		const result = await submitSupplyRequest(testData);
		testResult.value = `用品补充需求测试成功: ${JSON.stringify(result, null, 2)}`;
	} catch (error) {
		testResult.value = `用品补充需求测试失败: ${error.message}`;
		console.error('用品补充需求测试失败:', error);
	}
};

onMounted(() => {
	getUserInfo();
});
</script>

<style scoped>
.test-container {
	min-height: 100vh;
	background-color: #f8f9fa;
}

.test-content {
	padding: 20rpx;
}

.test-section {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.info-item {
	margin-bottom: 10rpx;
	font-size: 28rpx;
	color: #666;
}

.test-btn {
	width: 100%;
	height: 80rpx;
	background: linear-gradient(135deg, #007AFF, #5AC8FA);
	color: white;
	border: none;
	border-radius: 12rpx;
	font-size: 28rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.result-content {
	font-size: 24rpx;
	color: #333;
	background: #f5f5f5;
	padding: 20rpx;
	border-radius: 8rpx;
	word-break: break-all;
	white-space: pre-wrap;
}
</style>
