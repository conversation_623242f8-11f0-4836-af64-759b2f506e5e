package com.dfab.handicraftClass.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfab.handicraftClass.entity.HandicraftClass;
import com.dfab.handicraftClass.mapper.HandicraftClassMapper;
import com.dfab.handicraftClass.service.HandicraftClassService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 手工课安排服务实现类
 */
@Service
@Slf4j
public class HandicraftClassServiceImpl extends ServiceImpl<HandicraftClassMapper, HandicraftClass> implements HandicraftClassService {





    @Override
    public List<HandicraftClass> getClassesByType(Integer classType) {
        LambdaQueryWrapper<HandicraftClass> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HandicraftClass::getClassType, classType);
        wrapper.orderByDesc(HandicraftClass::getCreateTime);
        return list(wrapper);
    }

    @Override
    public List<HandicraftClass> getClassesByStatus(Integer status) {
        LambdaQueryWrapper<HandicraftClass> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HandicraftClass::getStatus, status);
        wrapper.orderByDesc(HandicraftClass::getCreateTime);
        return list(wrapper);
    }

    @Override
    public List<HandicraftClass> getClassesByTeacher(String teacher) {
        LambdaQueryWrapper<HandicraftClass> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HandicraftClass::getTeacher, teacher);
        wrapper.orderByDesc(HandicraftClass::getCreateTime);
        return list(wrapper);
    }

    @Override
    public List<HandicraftClass> getClassesByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<HandicraftClass> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(HandicraftClass::getClassTime, startTime, endTime);
        wrapper.orderByAsc(HandicraftClass::getClassTime);
        return list(wrapper);
    }



    @Override
    public HandicraftClass createClassByAdmin(HandicraftClass handicraftClass) {
        // 后台管理员创建课程，直接保存，不需要openId验证和自动填充
        save(handicraftClass);
        return handicraftClass;
    }

    @Override
    public Page<HandicraftClass> getClassesPageByMybatisPlus(int pageNum, int pageSize, HandicraftClass queryParams) {
        // 使用MyBatis-Plus的分页插件
        Page<HandicraftClass> page = new Page<>(pageNum, pageSize);

        LambdaQueryWrapper<HandicraftClass> wrapper = new LambdaQueryWrapper<>();

        // 根据查询参数构建查询条件
        if (queryParams != null) {
            if (queryParams.getClassType() != null) {
                wrapper.eq(HandicraftClass::getClassType, queryParams.getClassType());
            }
            if (queryParams.getStatus() != null) {
                wrapper.eq(HandicraftClass::getStatus, queryParams.getStatus());
            }
            if (StringUtils.hasText(queryParams.getClassName())) {
                wrapper.like(HandicraftClass::getClassName, queryParams.getClassName());
            }
            if (StringUtils.hasText(queryParams.getTeacher())) {
                wrapper.like(HandicraftClass::getTeacher, queryParams.getTeacher());
            }
            if (StringUtils.hasText(queryParams.getLocation())) {
                wrapper.like(HandicraftClass::getLocation, queryParams.getLocation());
            }
            if (queryParams.getClassTimeStart() != null) {
                wrapper.ge(HandicraftClass::getClassTime, queryParams.getClassTimeStart());
            }
            if (queryParams.getClassTimeEnd() != null) {
                wrapper.le(HandicraftClass::getClassTime, queryParams.getClassTimeEnd());
            }
        }

        wrapper.orderByDesc(HandicraftClass::getCreateTime);

        // 使用MyBatis-Plus的分页查询
        return page(page, wrapper);
    }

    @Override
    public boolean updateClassStatus(Long id, Integer status) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        
        LambdaUpdateWrapper<HandicraftClass> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(HandicraftClass::getId, id);
        updateWrapper.set(HandicraftClass::getStatus, status);
        
        return update(updateWrapper);
    }

    @Override
    public boolean assignTeacher(Long id, String teacher, String teacherPhone) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        
        LambdaUpdateWrapper<HandicraftClass> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(HandicraftClass::getId, id);
        updateWrapper.set(HandicraftClass::getTeacher, teacher);
        updateWrapper.set(HandicraftClass::getTeacherPhone, teacherPhone);
        
        return update(updateWrapper);
    }

    @Override
    public boolean completeClass(Long id, LocalDateTime actualStartTime, LocalDateTime actualEndTime) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        
        LambdaUpdateWrapper<HandicraftClass> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(HandicraftClass::getId, id);
        updateWrapper.set(HandicraftClass::getStatus, 2); // 2-已完成
        updateWrapper.set(HandicraftClass::getActualStartTime, actualStartTime);
        updateWrapper.set(HandicraftClass::getActualEndTime, actualEndTime);
        
        return update(updateWrapper);
    }

    @Override
    public boolean cancelClass(Long id, String cancelReason) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        
        LambdaUpdateWrapper<HandicraftClass> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(HandicraftClass::getId, id);
        updateWrapper.set(HandicraftClass::getStatus, 3); // 3-已取消
        updateWrapper.set(HandicraftClass::getCancelReason, cancelReason);
        
        return update(updateWrapper);
    }

    @Override
    public boolean rateClass(Long id, Integer rating, String review) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        
        LambdaUpdateWrapper<HandicraftClass> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(HandicraftClass::getId, id);
        updateWrapper.set(HandicraftClass::getRating, rating);
        updateWrapper.set(HandicraftClass::getReview, review);
        
        return update(updateWrapper);
    }

    @Override
    public boolean joinClass(Long id, String openId) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        
        HandicraftClass handicraftClass = getById(id);
        if (handicraftClass == null) {
            throw new RuntimeException("课程不存在");
        }
        
        // 检查是否还有空余名额
        if (handicraftClass.getMaxParticipants() != null && 
            handicraftClass.getCurrentParticipants() >= handicraftClass.getMaxParticipants()) {
            throw new RuntimeException("课程已满员");
        }
        
        // 增加参与人数
        LambdaUpdateWrapper<HandicraftClass> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(HandicraftClass::getId, id);
        updateWrapper.setSql("current_participants = current_participants + 1");
        
        return update(updateWrapper);
    }

    @Override
    public boolean leaveClass(Long id, String openId) {
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        
        // 减少参与人数
        LambdaUpdateWrapper<HandicraftClass> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(HandicraftClass::getId, id);
        updateWrapper.setSql("current_participants = GREATEST(current_participants - 1, 0)");
        
        return update(updateWrapper);
    }

    @Override
    public long getTodayClassCount(Integer classType) {
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.atTime(LocalTime.MAX);
        
        LambdaQueryWrapper<HandicraftClass> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(HandicraftClass::getClassTime, startOfDay, endOfDay);
        if (classType != null) {
            wrapper.eq(HandicraftClass::getClassType, classType);
        }
        
        return count(wrapper);
    }

    @Override
    public long getPendingClassCount(Integer classType) {
        LambdaQueryWrapper<HandicraftClass> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HandicraftClass::getStatus, 0); // 0-待开始
        if (classType != null) {
            wrapper.eq(HandicraftClass::getClassType, classType);
        }
        
        return count(wrapper);
    }

    @Override
    public List<HandicraftClass> getAvailableClasses() {
        LambdaQueryWrapper<HandicraftClass> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HandicraftClass::getStatus, 0); // 0-待开始
        wrapper.ge(HandicraftClass::getClassTime, LocalDateTime.now()); // 未来的课程
        wrapper.and(w -> w.isNull(HandicraftClass::getMaxParticipants)
                .or()
                .apply("current_participants < max_participants")); // 有空余名额
        wrapper.orderByAsc(HandicraftClass::getClassTime);

        return list(wrapper);
    }

    @Override
    public boolean updateCurrentParticipants(Long classId) {
        if (classId == null) {
            throw new RuntimeException("classId不能为空");
        }

        // 这里需要注入HandicraftClassParticipantService来计算实际参与人数
        // 为了避免循环依赖，我们使用SQL直接更新
        LambdaUpdateWrapper<HandicraftClass> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(HandicraftClass::getId, classId);
        updateWrapper.setSql("current_participants = (SELECT COUNT(*) FROM handicraft_class_participant WHERE class_id = " + classId + " AND status != 3)");

        return update(updateWrapper);
    }
}
