package com.dfab.handicraftClass.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 手工课安排实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("handicraft_class")
@Schema(description = "手工课安排实体类")
@Builder
public class HandicraftClass extends BaseEntity {
    
    /**
     * 手工课记录的唯一标识，系统自动分配的 ID。
     * 使用JsonSerialize将Long转为String，避免前端精度丢失
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "手工课记录 ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;



    /**
     * 课程名称
     */
    @Schema(description = "课程名称", example = "手工编织课", required = true)
    @Excel(name = "课程名称")
    private String className;

    /**
     * 课程类型：1-手工编织，2-手工制作，3-绘画课程，4-其他手工
     */
    @Schema(description = "课程类型：1-手工编织，2-手工制作，3-绘画课程，4-其他手工", example = "1", required = true)
    @Excel(name = "课程类型")
    private Integer classType;

    /**
     * 课程时间
     */
    @Schema(description = "课程时间", example = "2024-01-01 14:00:00", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "课程时间")
    private LocalDateTime classTime;

    /**
     * 课程时长（分钟）
     */
    @Schema(description = "课程时长（分钟）", example = "120")
    @Excel(name = "课程时长")
    private Integer duration;

    /**
     * 课程地点
     */
    @Schema(description = "课程地点", example = "手工活动室")
    @Excel(name = "课程地点")
    private String location;

    /**
     * 授课老师
     */
    @Schema(description = "授课老师", example = "李老师")
    @Excel(name = "授课老师")
    private String teacher;

    /**
     * 老师电话
     */
    @Schema(description = "老师电话", example = "13900139000")
    @Excel(name = "老师电话")
    private String teacherPhone;

    /**
     * 课程状态：0-待开始，1-进行中，2-已完成，3-已取消
     */
    @Schema(description = "课程状态：0-待开始，1-进行中，2-已完成，3-已取消", example = "0")
    @Excel(name = "课程状态")
    @Builder.Default
    private Integer status = 0;

    /**
     * 课程描述
     */
    @Schema(description = "课程描述", example = "学习基础的手工编织技巧")
    @Excel(name = "课程描述")
    private String description;

    /**
     * 所需材料
     */
    @Schema(description = "所需材料", example = "毛线、编织针、剪刀")
    @Excel(name = "所需材料")
    private String materials;

    /**
     * 课程难度：1-初级，2-中级，3-高级
     */
    @Schema(description = "课程难度：1-初级，2-中级，3-高级", example = "1")
    @Excel(name = "课程难度")
    private Integer difficulty;

    /**
     * 最大参与人数
     */
    @Schema(description = "最大参与人数", example = "8")
    @Excel(name = "最大参与人数")
    private Integer maxParticipants;

    /**
     * 当前参与人数
     */
    @Schema(description = "当前参与人数", example = "3")
    @Excel(name = "当前参与人数")
    @Builder.Default
    private Integer currentParticipants = 0;

    /**
     * 实际开始时间
     */
    @Schema(description = "实际开始时间", example = "2024-01-01 14:05:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "实际开始时间")
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    @Schema(description = "实际结束时间", example = "2024-01-01 16:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "实际结束时间")
    private LocalDateTime actualEndTime;

    /**
     * 课程费用
     */
    @Schema(description = "课程费用", example = "50.00")
    @Excel(name = "课程费用")
    private Double fee;

    /**
     * 取消原因
     */
    @Schema(description = "取消原因", example = "老师临时有事")
    @Excel(name = "取消原因")
    private String cancelReason;

    /**
     * 课程评价分数（1-5分）
     */
    @Schema(description = "课程评价分数", example = "5")
    @Excel(name = "课程评价分数")
    private Integer rating;

    /**
     * 课程评价内容
     */
    @Schema(description = "课程评价内容", example = "老师很专业，学到了很多")
    @Excel(name = "课程评价内容")
    private String review;

    /**
     * 课程图片（多张图片用逗号分隔）
     */
    @Schema(description = "课程图片", example = "image1.jpg,image2.jpg")
    @Excel(name = "课程图片")
    private String images;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息", example = "请提前10分钟到达")
    @Excel(name = "备注信息")
    private String remarks;

    // 非数据库字段，用于查询条件
    @TableField(exist = false)
    @Schema(description = "课程时间范围开始")
    private LocalDateTime classTimeStart;

    @TableField(exist = false)
    @Schema(description = "课程时间范围结束")
    private LocalDateTime classTimeEnd;

    // 分页参数
    @TableField(exist = false)
    @Schema(description = "页码")
    private Integer pageNum;

    @TableField(exist = false)
    @Schema(description = "每页大小")
    private Integer pageSize;
}
