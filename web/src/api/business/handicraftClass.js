import request from '@/utils/request'

// 查询手工课列表
export function listHandicraftClass(query) {
  return request({
    url: '/admin/handicraft-classes/page',
    method: 'post',
    data: query
  })
}

// 查询手工课详细
export function getHandicraftClass(data) {
  return request({
    url: '/admin/handicraft-classes/get',
    method: 'post',
    data: data
  })
}

// 新增手工课
export function addHandicraftClass(data) {
  return request({
    url: '/admin/handicraft-classes/save',
    method: 'post',
    data: data
  })
}

// 修改手工课
export function updateHandicraftClass(data) {
  return request({
    url: '/admin/handicraft-classes/update',
    method: 'post',
    data: data
  })
}

// 删除手工课
export function delHandicraftClass(data) {
  return request({
    url: '/admin/handicraft-classes/remove',
    method: 'post',
    data: data
  })
}

// 根据课程类型获取手工课列表
export function getClassesByType(data) {
  return request({
    url: '/admin/handicraft-classes/list-by-type',
    method: 'post',
    data: data
  })
}

// 根据会员入住记录ID获取手工课列表
export function getClassesByMember(data) {
  return request({
    url: '/admin/handicraft-classes/list-by-member',
    method: 'post',
    data: data
  })
}

// 更新课程状态
export function updateClassStatus(data) {
  return request({
    url: '/admin/handicraft-classes/update-status',
    method: 'post',
    data: data
  })
}

// 分配授课老师
export function assignTeacher(data) {
  return request({
    url: '/admin/handicraft-classes/assign-teacher',
    method: 'post',
    data: data
  })
}

// 完成课程
export function completeClass(data) {
  return request({
    url: '/admin/handicraft-classes/complete',
    method: 'post',
    data: data
  })
}

// 取消课程
export function cancelClass(data) {
  return request({
    url: '/admin/handicraft-classes/cancel',
    method: 'post',
    data: data
  })
}

// 获取课程类型列表
export function getClassTypes() {
  return request({
    url: '/admin/handicraft-classes/types',
    method: 'post'
  })
}

// 获取课程统计信息
export function getClassStatistics(data) {
  return request({
    url: '/admin/handicraft-classes/statistics',
    method: 'post',
    data: data
  })
}

// 获取可参与的课程列表
export function getAvailableClasses() {
  return request({
    url: '/admin/handicraft-classes/available',
    method: 'post'
  })
}
