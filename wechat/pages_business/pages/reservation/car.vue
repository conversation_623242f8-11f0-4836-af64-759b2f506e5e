<template>
	<view class="car-reservation-container">
		<custom-nav-bar title="用车预约"></custom-nav-bar>
		
		<view class="car-reservation-content">
			<!-- 我的用车预约记录 -->
			<view class="history-section" v-if="reservationList.length > 0">
				<view class="section-title">我的用车预约</view>
				<view class="reservation-list">
					<view
						class="reservation-item"
						v-for="(item, index) in reservationList"
						:key="item.id"
					>
						<view class="reservation-header">
							<view class="reservation-title">{{ item.title || '用车预约' }}</view>
							<view class="reservation-status" :class="getStatusClass(item.status)">
								{{ getStatusText(item.status) }}
							</view>
						</view>
						<view class="reservation-info">
							<view class="info-row">
								<text class="info-label">预约时间：</text>
								<text class="info-value">{{ formatDateTime(item.reservationTime) }}</text>
							</view>
							<view class="info-row" v-if="item.extendedProperties">
								<text class="info-label">路线：</text>
								<text class="info-value">{{ getExtendedProperty(item.extendedProperties, 'startLocation') }} → {{ getExtendedProperty(item.extendedProperties, 'destination') }}</text>
							</view>
							<view class="info-row" v-if="item.extendedProperties">
								<text class="info-label">乘车人数：</text>
								<text class="info-value">{{ getExtendedProperty(item.extendedProperties, 'passengerCount') || 1 }}人</text>
							</view>
						</view>
						<view class="reservation-description" v-if="item.description">
							{{ item.description }}
						</view>
					</view>
				</view>
			</view>

			<!-- 用车介绍 -->
			<view class="intro-section">
				<view class="intro-title">{{ reservationList.length > 0 ? '新增用车预约' : '用车服务' }}</view>
				<view class="intro-desc">
					我们提供专业的接送车辆服务，包括机场接送、医院接送、购物接送等。
					专业司机，安全舒适，让您的出行更加便捷。
				</view>
			</view>

			<!-- 预约表单 -->
			<view class="form-section">
				<view class="form-title">预约信息</view>
				
				<view class="form-item">
					<view class="form-label">联系人姓名</view>
					<input 
						class="form-input" 
						v-model="formData.contactName" 
						placeholder="请输入联系人姓名"
						maxlength="20"
					/>
				</view>

				<view class="form-item">
					<view class="form-label">联系电话</view>
					<input 
						class="form-input" 
						v-model="formData.contactPhone" 
						placeholder="请输入联系电话"
						type="number"
						maxlength="11"
					/>
				</view>

				<view class="form-item">
					<view class="form-label">用车日期</view>
					<picker 
						mode="date" 
						:value="formData.reservationDate" 
						:start="minDate"
						:end="maxDate"
						@change="onDateChange"
					>
						<view class="form-input picker-input">
							{{ formData.reservationDate || '请选择用车日期' }}
						</view>
					</picker>
				</view>

				<view class="form-item">
					<view class="form-label">用车时间</view>
					<picker 
						mode="time" 
						:value="formData.reservationTime" 
						@change="onTimeChange"
					>
						<view class="form-input picker-input">
							{{ formData.reservationTime || '请选择用车时间' }}
						</view>
					</picker>
				</view>

				<view class="form-item">
					<view class="form-label">乘车人数</view>
					<picker
						:range="passengerOptions"
						:value="formData.passengerIndex"
						@change="onPassengerChange"
					>
						<view class="form-input picker-input">
							{{ passengerOptions[formData.passengerIndex] || '请选择乘车人数' }}
						</view>
					</picker>
				</view>

				<view class="form-item">
					<view class="form-label">出发地点</view>
					<input 
						class="form-input" 
						v-model="formData.startLocation" 
						placeholder="请输入出发地点"
						maxlength="100"
					/>
				</view>

				<view class="form-item">
					<view class="form-label">目的地</view>
					<input 
						class="form-input" 
						v-model="formData.destination" 
						placeholder="请输入目的地"
						maxlength="100"
					/>
				</view>



				<view class="form-item">
					<view class="form-label">备注说明</view>
					<textarea 
						class="form-textarea" 
						v-model="formData.remarks" 
						placeholder="其他需要说明的信息（可选）"
						maxlength="200"
					></textarea>
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-section">
				<button 
					class="submit-btn" 
					@click="submitReservation"
					:disabled="submitting"
				>
					{{ submitting ? '提交中...' : '提交预约' }}
				</button>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { createCarReservation, getReservationList } from '@/config/api/reservation.js';
import { getShareAppMessageConfig, getShareTimelineConfig } from '@/utils/share.js';

// 表单数据
const formData = ref({
	contactName: '',
	contactPhone: '',
	reservationDate: '',
	reservationTime: '',
	passengerIndex: 0,
	passengerCount: 1,
	startLocation: '',
	destination: '',
	remarks: ''
});

// 用户信息
const userInfo = ref(null);

// 预约记录列表
const reservationList = ref([]);
const loading = ref(false);

// 乘车人数选项
const passengerOptions = ref(['1人', '2人', '3人', '4人', '5人', '6人', '7人以上']);

// 提交状态
const submitting = ref(false);

// 日期范围
const minDate = computed(() => {
	const today = new Date();
	return today.toISOString().split('T')[0];
});

const maxDate = computed(() => {
	const today = new Date();
	const maxDate = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000); // 30天后
	return maxDate.toISOString().split('T')[0];
});

// 日期选择
const onDateChange = (e) => {
	formData.value.reservationDate = e.detail.value;
};

// 时间选择
const onTimeChange = (e) => {
	formData.value.reservationTime = e.detail.value;
};

// 乘车人数选择
const onPassengerChange = (e) => {
	formData.value.passengerIndex = e.detail.value;
	formData.value.passengerCount = e.detail.value + 1;
};

// 表单验证
const validateForm = () => {
	if (!formData.value.contactName.trim()) {
		uni.showToast({
			title: '请输入联系人姓名',
			icon: 'none'
		});
		return false;
	}

	if (!formData.value.contactPhone.trim()) {
		uni.showToast({
			title: '请输入联系电话',
			icon: 'none'
		});
		return false;
	}

	// 验证手机号格式
	const phoneRegex = /^1[3-9]\d{9}$/;
	if (!phoneRegex.test(formData.value.contactPhone)) {
		uni.showToast({
			title: '请输入正确的手机号',
			icon: 'none'
		});
		return false;
	}

	if (!formData.value.reservationDate) {
		uni.showToast({
			title: '请选择用车日期',
			icon: 'none'
		});
		return false;
	}

	if (!formData.value.reservationTime) {
		uni.showToast({
			title: '请选择用车时间',
			icon: 'none'
		});
		return false;
	}

	if (!formData.value.startLocation.trim()) {
		uni.showToast({
			title: '请输入出发地点',
			icon: 'none'
		});
		return false;
	}

	if (!formData.value.destination.trim()) {
		uni.showToast({
			title: '请输入目的地',
			icon: 'none'
		});
		return false;
	}



	return true;
};

// 提交预约
const submitReservation = async () => {
	if (!validateForm()) {
		return;
	}

	submitting.value = true;

	try {
		// 构造预约时间
		const reservationDateTime = `${formData.value.reservationDate} ${formData.value.reservationTime}:00`;
		
		// 构造扩展属性
		const extendedProperties = {
			passengerCount: formData.value.passengerCount,
			startLocation: formData.value.startLocation,
			destination: formData.value.destination,
			remarks: formData.value.remarks
		};

		const requestData = {
			contactPhone: formData.value.contactPhone,
			userName: formData.value.contactName,
			reservationTime: reservationDateTime,
			description: `用车预约 - ${formData.value.passengerCount}人 - ${formData.value.startLocation}到${formData.value.destination}`,
			extendedProperties: JSON.stringify(extendedProperties)
		};

		console.log('提交用车预约:', requestData);

		await createCarReservation(requestData);

		uni.showToast({
			title: '预约成功',
			icon: 'success'
		});

		// 重新加载预约列表
		loadReservationList();

		// 重置表单
		resetForm();

	} catch (error) {
		console.error('提交用车预约失败:', error);
		uni.showToast({
			title: error.message || '预约失败，请重试',
			icon: 'none'
		});
	} finally {
		submitting.value = false;
	}
};

// 分享给朋友
const onShareAppMessage = () => {
	return getShareAppMessageConfig({
		title: '东方爱堡月子会所 - 用车预约',
		path: '/pages_business/pages/reservation/car',
	});
}

// 分享到朋友圈
const onShareTimeline = () => {
	return getShareTimelineConfig({
		title: '东方爱堡月子会所用车预约，专业接送服务',
	});
}

// 获取用户信息
const getUserInfo = () => {
	try {
		const userInfoStr = uni.getStorageSync('userInfo');
		if (userInfoStr) {
			userInfo.value = JSON.parse(userInfoStr);
			// 自动填充用户信息
			if (userInfo.value.wxNickname) {
				formData.value.contactName = userInfo.value.wxNickname;
			}
			if (userInfo.value.phoneNumber) {
				formData.value.contactPhone = userInfo.value.phoneNumber;
			}
		}
	} catch (error) {
		console.error('获取用户信息失败:', error);
	}
};

// 加载预约记录列表
const loadReservationList = async () => {
	loading.value = true;

	try {
		const params = {
			reservationType: 1 // 用车预约类型
		};

		console.log('加载用车预约列表，参数:', params);

		const response = await getReservationList(params);

		if (response && Array.isArray(response)) {
			reservationList.value = response;
		} else {
			reservationList.value = [];
		}

		console.log('用车预约列表加载成功:', reservationList.value);

	} catch (error) {
		console.error('加载用车预约列表失败:', error);
		reservationList.value = [];
	} finally {
		loading.value = false;
	}
};

// 重置表单
const resetForm = () => {
	formData.value.reservationDate = '';
	formData.value.reservationTime = '';
	formData.value.passengerIndex = 0;
	formData.value.passengerCount = 1;
	formData.value.startLocation = '';
	formData.value.destination = '';
	formData.value.remarks = '';
};

// 获取状态文本
const getStatusText = (status) => {
	const statusMap = {
		0: '待确认',
		1: '已确认',
		2: '已完成',
		3: '已取消'
	};
	return statusMap[status] || '未知状态';
};

// 获取状态样式类
const getStatusClass = (status) => {
	const classMap = {
		0: 'status-pending',
		1: 'status-confirmed',
		2: 'status-completed',
		3: 'status-cancelled'
	};
	return classMap[status] || '';
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
	if (!dateTimeStr) return '';
	const date = new Date(dateTimeStr);
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	const hour = String(date.getHours()).padStart(2, '0');
	const minute = String(date.getMinutes()).padStart(2, '0');
	return `${year}-${month}-${day} ${hour}:${minute}`;
};

// 获取扩展属性
const getExtendedProperty = (extendedPropertiesStr, key) => {
	try {
		if (!extendedPropertiesStr) return null;
		const properties = JSON.parse(extendedPropertiesStr);
		return properties[key];
	} catch (error) {
		console.error('解析扩展属性失败:', error);
		return null;
	}
};

onMounted(() => {
	console.log('用车预约页面加载完成');
	getUserInfo();
	loadReservationList();
});
</script>

<style scoped>
.car-reservation-container {
	min-height: 100vh;
	background-color: #f8f9fa;
}

.car-reservation-content {
	padding: 20rpx;
}

.history-section {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.reservation-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.reservation-item {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 25rpx;
	border-left: 4rpx solid #2196F3;
}

.reservation-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.reservation-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	flex: 1;
}

.reservation-status {
	padding: 6rpx 12rpx;
	border-radius: 16rpx;
	font-size: 22rpx;
	color: white;
}

.status-pending {
	background: #FF9800;
}

.status-confirmed {
	background: #4CAF50;
}

.status-completed {
	background: #9E9E9E;
}

.status-cancelled {
	background: #F44336;
}

.reservation-info {
	margin-bottom: 15rpx;
}

.info-row {
	display: flex;
	margin-bottom: 8rpx;
	font-size: 26rpx;
}

.info-label {
	color: #666;
	width: 160rpx;
}

.info-value {
	color: #333;
	flex: 1;
}

.reservation-description {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
	padding: 15rpx;
	background: white;
	border-radius: 8rpx;
}

.intro-section {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.intro-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.intro-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.form-section {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.form-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.form-item {
	margin-bottom: 30rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 15rpx;
	font-weight: 500;
}

.form-input, .picker-input {
	width: 100%;
	height: 80rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #333;
	background: #fff;
	box-sizing: border-box;
}

.picker-input {
	display: flex;
	align-items: center;
	color: #999;
}

.form-textarea {
	width: 100%;
	min-height: 120rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 20rpx;
	font-size: 28rpx;
	color: #333;
	background: #fff;
	box-sizing: border-box;
}

.submit-section {
	padding: 30rpx;
}

.submit-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #2196F3, #42A5F5);
	color: white;
	border: none;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
}

.submit-btn:disabled {
	background: #ccc;
}
</style>
