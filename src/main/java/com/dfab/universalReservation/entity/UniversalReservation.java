package com.dfab.universalReservation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dfab.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 通用预约实体类
 * 支持试餐、看房、用车等多种预约类型
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("universal_reservation")
@Schema(description = "通用预约实体类")
@Builder
public class UniversalReservation extends BaseEntity {
    
    /**
     * 预约记录的唯一标识，系统自动分配的 ID。
     * 使用JsonSerialize将Long转为String，避免前端精度丢失
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "预约记录 ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户的微信 openId
     */
    @Schema(description = "用户的微信 openId", example = "wx1234567890", required = true)
    @Excel(name = "用户的微信 openId")
    private String openId;

    /**
     * 用户id userId
     */
    @Schema(description = "用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 用户姓名
     */
    @Schema(description = "用户姓名")
    @Excel(name = "用户姓名")
    private String userName;

    /**
     * 用户电话
     */
    @Schema(description = "用户电话")
    @Excel(name = "用户电话")
    private String userPhone;

    /**
     * 会员入住记录ID，关联member_checkin表
     */
    @Schema(description = "会员入住记录ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    @Excel(name = "会员入住记录ID")
    private Long memberCheckinId;

    /**
     * 预约类型：1-餐食预约，2-用车预约，3-试餐预约，4-看房预约，5-其他预约
     */
    @Schema(description = "预约类型：1-餐食预约，2-用车预约，3-试餐预约，4-看房预约，5-其他预约", example = "1", required = true)
    @Excel(name = "预约类型")
    private Integer reservationType;

    /**
     * 预约标题
     */
    @Schema(description = "预约标题", example = "试餐预约", required = true)
    @Excel(name = "预约标题")
    private String title;

    /**
     * 预约时间
     */
    @Schema(description = "预约时间", example = "2024-01-01 10:00:00", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "预约时间")
    private LocalDateTime reservationTime;

    /**
     * 预约结束时间（可选，用于时间段预约）
     */
    @Schema(description = "预约结束时间", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "预约结束时间")
    private LocalDateTime endTime;

    /**
     * 预约地点
     */
    @Schema(description = "预约地点", example = "月子中心餐厅")
    @Excel(name = "预约地点")
    private String location;

    /**
     * 预约人数
     */
    @Schema(description = "预约人数", example = "2")
    @Excel(name = "预约人数")
    private Integer peopleCount;

    /**
     * 预约状态：0-待确认，1-已确认，2-已完成，3-已取消
     */
    @Schema(description = "预约状态：0-待确认，1-已确认，2-已完成，3-已取消", example = "0")
    @Excel(name = "预约状态")
    @Builder.Default
    private Integer status = 0;

    /**
     * 预约描述/备注
     */
    @Schema(description = "预约描述/备注", example = "特殊需求说明")
    @Excel(name = "预约描述")
    private String description;

    /**
     * 联系电话（可能与用户电话不同）
     */
    @Schema(description = "联系电话", example = "13800138000")
    @Excel(name = "联系电话")
    private String contactPhone;

    /**
     * 房间号
     */
    @Schema(description = "房间号", example = "A101")
    @Excel(name = "房间号")
    private String roomNumber;

    /**
     * 扩展属性（JSON格式，存储不同预约类型的特殊字段）
     * 例如：
     * 餐食预约：{"mealTime": "Lunch", "quantity": 2}
     * 用车预约：{"carType": "轿车", "destination": "机场", "driverName": "张师傅"}
     * 试餐预约：{"cuisineType": "川菜", "allergyInfo": "不吃辣"}
     * 看房预约：{"roomType": "标准间", "preferredFloor": "3楼"}
     */
    @Schema(description = "扩展属性（JSON格式）", example = "{\"mealTime\": \"Lunch\", \"quantity\": 2}")
    @Excel(name = "扩展属性")
    private String extendedProperties;

    /**
     * 处理人员姓名（如司机、服务员等）
     */
    @Schema(description = "处理人员姓名", example = "张师傅")
    @Excel(name = "处理人员姓名")
    private String handlerName;

    /**
     * 处理人员电话
     */
    @Schema(description = "处理人员电话", example = "13900139000")
    @Excel(name = "处理人员电话")
    private String handlerPhone;

    /**
     * 实际开始时间
     */
    @Schema(description = "实际开始时间", example = "2024-01-01 10:05:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "实际开始时间")
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    @Schema(description = "实际结束时间", example = "2024-01-01 11:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "实际结束时间")
    private LocalDateTime actualEndTime;

    /**
     * 费用金额
     */
    @Schema(description = "费用金额", example = "100.00")
    @Excel(name = "费用金额")
    private Double amount;

    /**
     * 取消原因
     */
    @Schema(description = "取消原因", example = "行程变更")
    @Excel(name = "取消原因")
    private String cancelReason;

    /**
     * 评价分数（1-5分）
     */
    @Schema(description = "评价分数", example = "5")
    @Excel(name = "评价分数")
    private Integer rating;

    /**
     * 评价内容
     */
    @Schema(description = "评价内容", example = "服务很好")
    @Excel(name = "评价内容")
    private String review;

    // 非数据库字段，用于查询条件
    @TableField(exist = false)
    @Schema(description = "预约时间范围开始")
    private LocalDateTime reservationTimeStart;

    @TableField(exist = false)
    @Schema(description = "预约时间范围结束")
    private LocalDateTime reservationTimeEnd;

    // 分页参数
    @TableField(exist = false)
    @Schema(description = "页码")
    private Integer pageNum;

    @TableField(exist = false)
    @Schema(description = "每页大小")
    private Integer pageSize;
}
