package com.dfab.handicraftClass.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dfab.handicraftClass.entity.HandicraftClass;
import com.dfab.handicraftClass.entity.HandicraftClassParticipant;
import com.dfab.handicraftClass.enums.ClassTypeEnum;
import com.dfab.handicraftClass.service.HandicraftClassService;
import com.dfab.handicraftClass.service.HandicraftClassParticipantService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序手工课安排控制器
 */
@RestController
@RequestMapping("/api/handicraftClass")
@Tag(name = "HandicraftClassMiniController", description = "小程序手工课安排接口，提供对手工课程的查看和参与功能")
public class HandicraftClassMiniController {

    @Autowired
    private HandicraftClassService handicraftClassService;

    @Autowired
    private HandicraftClassParticipantService participantService;

    @Autowired
    private HttpServletRequest request;

    /**
     * 根据 ID 获取手工课信息
     * @param handicraftClass 包含课程ID的对象
     * @return 对应的手工课信息
     */
    @Operation(summary = "根据 ID 获取手工课信息", description = "通过指定的课程记录 ID 查询对应的手工课信息")
    @PostMapping("/get")
    @Log(title = "小程序手工课安排-根据ID获取课程信息", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public HandicraftClass getClassById(@Parameter(description = "课程记录的 ID", required = true) @RequestBody HandicraftClass handicraftClass) {
        return handicraftClassService.getById(handicraftClass.getId());
    }

    /**
     * 查询手工课列表
     * @param handicraftClass 查询参数
     * @return 手工课列表
     */
    @Operation(summary = "查询手工课列表", description = "根据查询条件获取手工课列表")
    @PostMapping("/list")
    @Log(title = "小程序手工课安排-查询手工课列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public List<HandicraftClass> getClassesList(@RequestBody(required = false) HandicraftClass handicraftClass) {
        if (handicraftClass == null) {
            handicraftClass = new HandicraftClass();
        }

        LambdaQueryWrapper<HandicraftClass> wrapper = new LambdaQueryWrapper<>();

        // 根据查询参数构建查询条件
        if (handicraftClass.getClassType() != null) {
            wrapper.eq(HandicraftClass::getClassType, handicraftClass.getClassType());
        }
        if (handicraftClass.getStatus() != null) {
            wrapper.eq(HandicraftClass::getStatus, handicraftClass.getStatus());
        }

        wrapper.orderByDesc(HandicraftClass::getCreateTime);

        return handicraftClassService.list(wrapper);
    }

    /**
     * 获取可参与的课程列表
     * @return 可参与的课程列表
     */
    @Operation(summary = "获取可参与的课程列表", description = "获取有空余名额的课程列表")
    @PostMapping("/available")
    @Log(title = "小程序手工课安排-获取可参与课程列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public List<HandicraftClass> getAvailableClasses() {
        return handicraftClassService.getAvailableClasses();
    }

    /**
     * 根据 openId 获取用户参与的手工课列表
     * @return 对应 openId 的用户参与的手工课列表
     */
    @Operation(summary = "获取我的课程列表", description = "通过用户的微信 openId 查询用户参与的手工课列表")
    @PostMapping("/my-classes")
    @Log(title = "小程序手工课安排-获取我的课程列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public List<HandicraftClass> getMyClasses() {
        String openId = (String) request.getAttribute("openId");
        return participantService.getParticipatedClassesByOpenId(openId);
    }

    /**
     * 参与手工课
     * @param handicraftClass 包含课程ID的对象
     * @return 参与结果
     */
    @Operation(summary = "参与手工课", description = "用户参与指定的手工课程")
    @PostMapping("/join")
    @Log(title = "小程序手工课安排-参与课程", businessType = BusinessType.INSERT, operatorType = OperatorType.MOBILE)
    public HandicraftClassParticipant joinClass(@RequestBody HandicraftClass handicraftClass) {
        String openId = (String) request.getAttribute("openId");
        return participantService.joinClass(handicraftClass.getId(), openId);
    }

    /**
     * 退出手工课
     * @param handicraftClass 包含课程ID的对象
     * @return 退出结果
     */
    @Operation(summary = "退出手工课", description = "用户退出指定的手工课程")
    @PostMapping("/leave")
    @Log(title = "小程序手工课安排-退出课程", businessType = BusinessType.DELETE, operatorType = OperatorType.MOBILE)
    public Map<String, Object> leaveClass(@RequestBody HandicraftClass handicraftClass) {
        String openId = (String) request.getAttribute("openId");
        boolean result = participantService.leaveClass(handicraftClass.getId(), openId);
        Map<String, Object> response = new HashMap<>();
        response.put("success", result);
        response.put("message", result ? "退出成功" : "退出失败");
        return response;
    }

    /**
     * 获取课程类型列表
     * @return 课程类型列表
     */
    @Operation(summary = "获取课程类型列表", description = "获取所有支持的课程类型")
    @PostMapping("/types")
    @Log(title = "小程序手工课安排-获取课程类型列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public Map<String, Object> getClassTypes() {
        Map<String, Object> result = new HashMap<>();
        Map<Integer, String> types = new HashMap<>();
        for (ClassTypeEnum type : ClassTypeEnum.values()) {
            types.put(type.getCode(), type.getName());
        }
        result.put("types", types);
        return result;
    }

    /**
     * 获取用户课程统计
     * @return 用户课程统计信息
     */
    @Operation(summary = "获取用户课程统计", description = "获取当前用户的课程统计信息")
    @PostMapping("/my-statistics")
    @Log(title = "小程序手工课安排-获取用户课程统计", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public Map<String, Object> getMyClassStatistics() {
        String openId = (String) request.getAttribute("openId");
        return participantService.getUserParticipationStatistics(openId);
    }
}