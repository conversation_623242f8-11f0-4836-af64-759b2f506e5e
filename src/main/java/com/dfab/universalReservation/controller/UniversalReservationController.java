package com.dfab.universalReservation.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dfab.universalReservation.entity.UniversalReservation;
import com.dfab.universalReservation.enums.ReservationTypeEnum;
import com.dfab.universalReservation.service.UniversalReservationService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用预约后台管理控制器
 */
@RestController
@RequestMapping("/admin/universal-reservations")
@RequiredArgsConstructor
@Tag(name = "UniversalReservationController", description = "通用预约管理接口，支持试餐、看房、用车等多种预约类型的统一管理")
public class UniversalReservationController {

    private final UniversalReservationService universalReservationService;

    /**
     * 分页查询预约列表
     * @param reservation 查询参数（包含分页信息）
     * @return 分页结果
     */
    @Operation(summary = "分页查询预约列表", description = "根据查询条件分页获取预约列表，支持多种预约类型")
    @PostMapping("/page")
    public Page<UniversalReservation> getReservationsPage(
            @Parameter(description = "查询参数（包含分页信息）") @RequestBody(required = false) UniversalReservation reservation) {
        if (reservation == null) {
            reservation = new UniversalReservation();
        }
        // 设置默认分页参数
        int pageNum = reservation.getPageNum() != null ? reservation.getPageNum() : 1;
        int pageSize = reservation.getPageSize() != null ? reservation.getPageSize() : 10;

        return universalReservationService.getReservationsPageByMybatisPlus(pageNum, pageSize, reservation);
    }

    /**
     * 根据ID获取预约详情
     * @param reservation 包含预约ID的对象
     * @return 预约详情
     */
    @Operation(summary = "根据ID获取预约详情", description = "通过预约ID获取单个预约的详细信息")
    @PostMapping("/get")
    public ResponseEntity<UniversalReservation> getReservationById(
            @Parameter(description = "预约ID", required = true) @RequestBody UniversalReservation reservation) {
        try {
            UniversalReservation result = universalReservationService.getById(reservation.getId());
            if (result != null) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 创建预约
     * @param reservation 预约信息
     * @return 创建结果
     */
    @Operation(summary = "创建预约", description = "创建新的预约记录")
    @PostMapping("/save")
    @Log(title = "通用预约管理-新增", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE)
    public ResponseEntity<?> createReservation(
            @Parameter(description = "预约信息", required = true)  @RequestBody UniversalReservation reservation) {
        try {
            universalReservationService.createReservationByAdmin(reservation);
            return ResponseEntity.ok(reservation);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 更新预约
     * @param reservation 预约信息
     * @return 更新结果
     */
    @Operation(summary = "更新预约", description = "根据ID更新预约信息")
    @PostMapping("/update")
    @Log(title = "通用预约管理-修改", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public ResponseEntity<?> updateReservation(
            @Parameter(description = "预约信息", required = true)@RequestBody UniversalReservation reservation) {
        try {
            universalReservationService.updateById(reservation);
            return ResponseEntity.ok(reservation);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 删除预约
     * @param reservation 包含预约ID的对象
     * @return 删除结果
     */
    @Operation(summary = "删除预约", description = "根据ID删除预约")
    @PostMapping("/remove")
    @Log(title = "通用预约管理-删除", businessType = BusinessType.DELETE, operatorType = OperatorType.MANAGE)
    public ResponseEntity<?> deleteReservation(
            @Parameter(description = "预约ID", required = true) @RequestBody UniversalReservation reservation) {
        try {
            universalReservationService.removeById(reservation.getId());
            return ResponseEntity.ok("删除成功");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 根据预约类型获取预约列表
     * @param reservation 包含预约类型的对象
     * @return 预约列表
     */
    @Operation(summary = "根据预约类型获取预约列表", description = "通过预约类型查询对应的预约列表")
    @PostMapping("/list-by-type")
    public ResponseEntity<List<UniversalReservation>> getReservationsByType(
            @Parameter(description = "预约类型", required = true) @RequestBody UniversalReservation reservation) {
        try {
            List<UniversalReservation> reservations = universalReservationService.getReservationsByType(reservation.getReservationType());
            return ResponseEntity.ok(reservations);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 根据会员入住记录ID获取预约列表
     * @param reservation 包含会员入住记录ID的对象
     * @return 预约列表
     */
    @Operation(summary = "根据会员入住记录ID获取预约列表", description = "通过会员入住记录ID查询对应的预约列表")
    @PostMapping("/list-by-member")
    public ResponseEntity<List<UniversalReservation>> getReservationsByMemberCheckinId(
            @Parameter(description = "会员入住记录ID", required = true) @RequestBody UniversalReservation reservation) {
        try {
            List<UniversalReservation> reservations = universalReservationService.getReservationsByMemberCheckinId(reservation.getMemberCheckinId());
            return ResponseEntity.ok(reservations);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 更新预约状态
     * @param reservation 包含预约ID和状态的对象
     * @return 更新结果
     */
    @Operation(summary = "更新预约状态", description = "根据ID更新预约状态")
    @PostMapping("/update-status")
    @Log(title = "通用预约管理-更新状态", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public ResponseEntity<?> updateReservationStatus(
            @Parameter(description = "预约信息", required = true) @RequestBody UniversalReservation reservation) {
        try {
            boolean result = universalReservationService.updateReservationStatus(reservation.getId(), reservation.getStatus());
            if (result) {
                return ResponseEntity.ok("状态更新成功");
            } else {
                return ResponseEntity.badRequest().body("状态更新失败");
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 分配处理人员
     * @param reservation 包含预约ID和处理人员信息的对象
     * @return 分配结果
     */
    @Operation(summary = "分配处理人员", description = "为预约分配处理人员")
    @PostMapping("/assign-handler")
    public ResponseEntity<?> assignHandler(
            @Parameter(description = "预约和处理人员信息", required = true) @RequestBody UniversalReservation reservation) {
        try {
            boolean result = universalReservationService.assignHandler(
                    reservation.getId(), 
                    reservation.getHandlerName(), 
                    reservation.getHandlerPhone()
            );
            if (result) {
                return ResponseEntity.ok("处理人员分配成功");
            } else {
                return ResponseEntity.badRequest().body("处理人员分配失败");
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 完成预约
     * @param reservation 包含预约ID和实际时间的对象
     * @return 完成结果
     */
    @Operation(summary = "完成预约", description = "标记预约为已完成并记录实际时间")
    @PostMapping("/complete")
    public ResponseEntity<?> completeReservation(
            @Parameter(description = "预约和实际时间信息", required = true) @RequestBody UniversalReservation reservation) {
        try {
            boolean result = universalReservationService.completeReservation(
                    reservation.getId(), 
                    reservation.getActualStartTime(), 
                    reservation.getActualEndTime()
            );
            if (result) {
                return ResponseEntity.ok("预约完成成功");
            } else {
                return ResponseEntity.badRequest().body("预约完成失败");
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 取消预约
     * @param reservation 包含预约ID和取消原因的对象
     * @return 取消结果
     */
    @Operation(summary = "取消预约", description = "取消预约并记录取消原因")
    @PostMapping("/cancel")
    public ResponseEntity<?> cancelReservation(
            @Parameter(description = "预约和取消原因", required = true) @RequestBody UniversalReservation reservation) {
        try {
            boolean result = universalReservationService.cancelReservation(reservation.getId(), reservation.getCancelReason());
            if (result) {
                return ResponseEntity.ok("预约取消成功");
            } else {
                return ResponseEntity.badRequest().body("预约取消失败");
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 获取预约类型列表
     * @return 预约类型列表
     */
    @Operation(summary = "获取预约类型列表", description = "获取所有支持的预约类型")
    @PostMapping("/types")
    public ResponseEntity<Map<String, Object>> getReservationTypes() {
        try {
            Map<String, Object> result = new HashMap<>();
            Map<Integer, String> types = new HashMap<>();
            for (ReservationTypeEnum type : ReservationTypeEnum.values()) {
                types.put(type.getCode(), type.getName());
            }
            result.put("types", types);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取预约统计信息
     * @param reservation 包含预约类型的对象（可选）
     * @return 统计信息
     */
    @Operation(summary = "获取预约统计信息", description = "获取今日预约数量和待处理预约数量")
    @PostMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getReservationStatistics(
            @Parameter(description = "预约类型（可选）") @RequestBody(required = false) UniversalReservation reservation) {
        try {
            Map<String, Object> result = new HashMap<>();
            Integer reservationType = reservation != null ? reservation.getReservationType() : null;
            
            long todayCount = universalReservationService.getTodayReservationCount(reservationType);
            long pendingCount = universalReservationService.getPendingReservationCount(reservationType);
            
            result.put("todayCount", todayCount);
            result.put("pendingCount", pendingCount);
            result.put("reservationType", reservationType);
            result.put("reservationTypeName", ReservationTypeEnum.getNameByCode(reservationType));
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
}
