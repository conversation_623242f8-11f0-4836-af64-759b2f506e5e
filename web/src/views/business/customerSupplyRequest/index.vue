<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户手机" prop="userPhone">
        <el-input
          v-model="queryParams.userPhone"
          placeholder="请输入用户手机号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="处理状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择处理状态" clearable style="width: 200px">
          <el-option label="待处理" :value="0" />
          <el-option label="已处理" :value="1" />
          <el-option label="已拒绝" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="需求内容" prop="requestContent">
        <el-input
          v-model="queryParams.requestContent"
          placeholder="请输入需求内容关键词"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleProcess"
          v-hasPermi="['business:customerSupplyRequest:process']"
        >处理</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['business:customerSupplyRequest:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 统计信息 -->
    <el-row :gutter="20" class="mb8">
      <el-col :span="6">
        <el-card class="box-card">
          <div class="statistic-item">
            <div class="statistic-title">待处理</div>
            <div class="statistic-value">{{ statistics.pendingCount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="statistic-item">
            <div class="statistic-title">已处理</div>
            <div class="statistic-value">{{ statistics.processedCount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="statistic-item">
            <div class="statistic-title">已拒绝</div>
            <div class="statistic-value">{{ statistics.rejectedCount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="statistic-item">
            <div class="statistic-title">总计</div>
            <div class="statistic-value">{{ statistics.totalCount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="customerSupplyRequestList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" width="50" label="序号" align="center" />

      <el-table-column label="用户信息" align="center" width="150">
        <template #default="scope">
          <div v-if="scope.row.userName">{{ scope.row.userName }}</div>
          <div v-if="scope.row.userPhone" style="font-size: 12px; color: #999;">{{ scope.row.userPhone }}</div>
          <span v-if="!scope.row.userName && !scope.row.userPhone" style="color: #999;">未知用户</span>
        </template>
      </el-table-column>
      <el-table-column label="需求内容" align="center" prop="requestContent" width="300" show-overflow-tooltip />
      <el-table-column label="处理状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag :type="getStatusTag(scope.row.status).type">
            {{ getStatusTag(scope.row.status).text }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="管理员备注" align="center" prop="adminRemark" width="200" show-overflow-tooltip />
      <el-table-column label="处理人" align="center" prop="processBy" width="100" />
      <el-table-column label="处理时间" align="center" prop="processTime" width="160">
        <template #default="scope">
          <span v-if="scope.row.processTime">{{ parseTime(scope.row.processTime, '{y}-{m}-{d} {h}:{i}') }}</span>
          <span v-else style="color: #999;">未处理</span>
        </template>
      </el-table-column>
      <el-table-column label="提交时间" align="center" prop="createTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['business:customerSupplyRequest:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleProcess(scope.row)" v-hasPermi="['business:customerSupplyRequest:process']">处理</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 处理对话框 -->
    <el-dialog :title="processTitle" v-model="processOpen" width="600px" append-to-body>
      <el-form ref="processRef" :model="processForm" :rules="processRules" label-width="80px">

        <el-form-item label="用户信息">
          <div>
            <div v-if="processForm.userName">姓名：{{ processForm.userName }}</div>
            <div v-if="processForm.userPhone">手机：{{ processForm.userPhone }}</div>
          </div>
        </el-form-item>
        <el-form-item label="需求内容">
          <el-input
            v-model="processForm.requestContent"
            type="textarea"
            :rows="3"
            readonly
          />
        </el-form-item>
        <el-form-item label="处理状态" prop="status">
          <el-radio-group v-model="processForm.status">
            <el-radio :label="1">已处理</el-radio>
            <el-radio :label="2">已拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="管理员备注" prop="adminRemark">
          <el-input
            v-model="processForm.adminRemark"
            type="textarea"
            :rows="3"
            placeholder="请输入处理备注"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitProcess">确 定</el-button>
          <el-button @click="cancelProcess">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog title="需求详情" v-model="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>

        <el-descriptions-item label="用户姓名">{{ detailForm.userName || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="用户手机">{{ detailForm.userPhone || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="处理状态">
          <el-tag :type="getStatusTag(detailForm.status).type">
            {{ getStatusTag(detailForm.status).text }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="提交时间">{{ parseTime(detailForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="需求内容" :span="2">{{ detailForm.requestContent }}</el-descriptions-item>
        <el-descriptions-item label="管理员备注" :span="2">{{ detailForm.adminRemark || '无' }}</el-descriptions-item>
        <el-descriptions-item label="处理人">{{ detailForm.processBy || '未处理' }}</el-descriptions-item>
        <el-descriptions-item label="处理时间">{{ detailForm.processTime ? parseTime(detailForm.processTime, '{y}-{m}-{d} {h}:{i}:{s}') : '未处理' }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CustomerSupplyRequest">
import {
  pageCustomerSupplyRequest,
  getCustomerSupplyRequest,
  processCustomerSupplyRequest
} from "@/api/business/customerSupplyRequest";
import { getCurrentInstance, ref, reactive, toRefs, onMounted } from 'vue';
import { parseTime } from "@/utils/ruoyi";

const { proxy } = getCurrentInstance();

const customerSupplyRequestList = ref([]);
const processOpen = ref(false);
const detailOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const processTitle = ref("");

// 统计数据
const statistics = ref({
  pendingCount: 0,
  processedCount: 0,
  rejectedCount: 0,
  totalCount: 0
});

const data = reactive({
  processForm: {},
  detailForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: null,
    userPhone: null,
    status: null,
    requestContent: null
  },
  processRules: {
    status: [
      { required: true, message: "处理状态不能为空", trigger: "change" }
    ],
    adminRemark: [
      { required: true, message: "管理员备注不能为空", trigger: "blur" },
      { max: 500, message: "管理员备注长度不能超过500个字符", trigger: "blur" }
    ]
  }
});

const { queryParams, processForm, detailForm, processRules } = toRefs(data);

/** 查询客户用品补充需求列表 */
function getList() {
  loading.value = true;
  pageCustomerSupplyRequest(queryParams.value).then(response => {
    console.log('后台管理API响应:', response);

    if (response && response.records ) {
      customerSupplyRequestList.value = response.records;
      total.value = response.total || 0;

      // 计算统计数据
      statistics.value = {
        pendingCount: response.records.filter(item => item.status === 0).length,
        processedCount: response.records.filter(item => item.status === 1).length,
        rejectedCount: response.records.filter(item => item.status === 2).length,
        totalCount: response.records.length
      };
    } else {
      customerSupplyRequestList.value = [];
      total.value = 0;
      statistics.value = {
        pendingCount: 0,
        processedCount: 0,
        rejectedCount: 0,
        totalCount: 0
      };
    }

    loading.value = false;
  }).catch(error => {
    console.error('API调用失败:', error);
    customerSupplyRequestList.value = [];
    total.value = 0;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  processOpen.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  processForm.value = {
    id: null,
    userName: null,
    userPhone: null,
    requestContent: null,
    status: null,
    adminRemark: null
  };
  proxy.resetForm("processRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 处理按钮操作 */
function handleProcess(row) {
  reset();
  const id = row.id || ids.value[0];
  getCustomerSupplyRequest({ id: id }).then(response => {
    processForm.value = response.data;
    processForm.value.status = processForm.value.status === 0 ? 1 : processForm.value.status;
    processOpen.value = true;
    processTitle.value = "处理需求";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  detailForm.value = row;
  detailOpen.value = true;
}

/** 提交处理 */
function submitProcess() {
  proxy.$refs["processRef"].validate(valid => {
    if (valid) {
      processCustomerSupplyRequest(processForm.value).then(response => {
        proxy.$modal.msgSuccess("处理成功");
        processOpen.value = false;
        getList();
      });
    }
  });
}

/** 取消处理 */
function cancelProcess() {
  processOpen.value = false;
  reset();
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('admin/customerSupplyRequest/export', {
    ...queryParams.value
  }, `客户用品补充需求_${new Date().getTime()}.xlsx`)
}

/** 获取状态标签 */
function getStatusTag(status) {
  const statusMap = {
    0: { type: 'warning', text: '待处理' },
    1: { type: 'success', text: '已处理' },
    2: { type: 'danger', text: '已拒绝' }
  };
  return statusMap[status] || { type: 'info', text: '未知' };
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.statistic-item {
  text-align: center;
}

.statistic-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.statistic-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.box-card {
  margin-bottom: 10px;
}
</style>
