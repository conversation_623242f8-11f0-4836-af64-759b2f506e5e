<template>
	<view class="index-container">
		<!-- 顶部宣传横幅 -->
		<view class="banner-section">
			<image class="banner-image" :src="lobbyPng" mode="aspectFill"></image>
			<view class="banner-overlay" :style="{ top: `${navBarHeight}rpx` }">
				<text class="banner-title">东方爱堡（夏雨馨竹）月子会所</text>
			</view>
		</view>

		<!-- 欢迎信息条 -->
		<view class="welcome-bar">
			<up-icon name="volume-fill" color="#fff" size="20"></up-icon>
			<view class="welcome-text">您好！欢迎来到东方爱堡（夏雨馨竹）月子会所</view>
		</view>

		<!-- 功能导航区 -->
		<view class="nav-section">
			<view class="nav-grid">
				<!-- 第一行导航 -->
				<view class="nav-item" v-for="(item, index) in navList.slice(0, 5)" :key="index"
					@click="navigateTo(item.url)">
					<view class="nav-icon">
						<image :src="item.icon" mode="aspectFit"></image>
					</view>
					<view class="nav-name">{{item.name}}</view>
				</view>
			</view>
		</view>

		<view class="divider-line"></view>

		<!-- 联系我们 -->
		<view class="contact-section">
			<view class="contact-left">
				<view class="contact-header">
					<text class="contact-title">联系我们</text>
					<text class="contact-subtitle">月子会所等问题，会所官方在线答疑</text>
				</view>
				<view class="contact-buttons">
					<view class="contact-button" @click="makePhoneCall">
						<up-icon name="phone-fill" color="#fff" size="20"></up-icon>
						<text class="contact-button-text">拨打电话</text>
					</view>
					<view class="contact-button" @click="showWechatQR">
						<up-icon name="weixin-fill" color="#fff" size="20"></up-icon>
						<text class="contact-button-text">添加微信</text>
					</view>
				</view>
				<!-- 分享按钮区域 -->
				<!-- <view class="share-buttons">
					<button class="share-button" open-type="share">
						<up-icon name="share-fill" color="#fff" size="18"></up-icon>
						<text class="share-button-text">分享给朋友</text>
					</button>
				</view> -->
			</view>
			<view class="contact-right">
				<image class="banner-image" src="/static/icon/contactRight.png" mode="aspectFill"></image>
			</view>

		</view>

		<view class="divider-line"></view>

		<!-- 合作商家 -->
		<view class="partners-section">
			<view class="section-header">
				<text class="section-title">合作商家</text>
				<text class="section-subtitle">优质商家资源，为您提供更好的服务</text>
			</view>
			<view class="partners-grid">
				<view class="partner-item" v-for="(partner, index) in partnerList" :key="index">
					<image class="partner-logo" :src="partner.logo" mode="aspectFit"></image>
				</view>
			</view>
		</view>
	</view>

	<!-- 微信二维码弹窗 -->
	<up-popup :show="showQRCode" @close="closeQRCode" mode="center" width="600rpx" height="auto" :round="10">
		<view class="qrcode-popup">
			<view class="qrcode-title">扫码添加微信</view>
			<image
				class="qrcode-image"
				src="/static/wechat-qrcode.jpg"
				mode="aspectFit"
				show-menu-by-longpress
				@longpress="onQRCodeLongPress"
			></image>
			<view class="qrcode-desc">微信号：DFAB2894163</view>
			<view class="qrcode-hint">长按二维码识别添加微信</view>
			<view class="close-button" @click="closeQRCode">关闭</view>
		</view>
	</up-popup>
</template>
<script setup>
	import {
		ref,
		onMounted
	} from 'vue';
	import {
		navigateTo
	} from '@/utils/utils.js';
	import {
		getShareAppMessageConfig,
		getShareTimelineConfig
	} from '@/utils/share.js';
	const navBarHeight = ref(0);
	const lobbyPng = ref('https://xiayuxinzhu.cn/api/files/previewResized/1926277348357857282/large')
	const showQRCode = ref(false);

	// 合作商家列表
	const partnerList = ref([
		{
			name: '玛瑞莎摄影',
			logo: '/static/hezuoshangjia1.jpg'
		}
    ,
		{
			name: '十月结晶',
			logo: '/static/hezuoshangjia2.jpg'
		}
	]);

	const navList = ref([
	// {
	// 		icon: '/static/icon/logo.png',
	// 		name: '品牌实力',
	// 		url: '/pages/intro/intro'
	// 	},
	// 	{
	// 		icon: '/static/icon/team.png',
	// 		name: '专业团队',
	// 		url: '/pages/team/team'
	// 	},
		{
			icon: '/static/icon/environment.png',
			name: '优质环境',
			url: '/pages_business/pages/intro/intro'
		},
		{
			icon: '/static/icon/roomType.png',
			name: '房型展示',
			url: '/pages_business/pages/room/room'
		},
		{
			icon: '/static/icon/meal.png',
			name: '营养餐食',
			url: '/pages_business/pages/food/food'
		},
		{
			icon: '/static/icon/nursing.png',
			name: '产康护理',
			url: '/pages_business/pages/recovery/recovery'
		},
		{
			icon: '/static/icon/baby.png',
			name: '宝宝护理',
			url: '/pages_business/pages/baby/baby'
		},
		{
			icon: '/static/icon/handMade.png',
			name: '手工课安排',
			url: '/pages_business/pages/handicraft/list'
		},
		{
			icon: '/static/icon/car.png',
			name: '用车预约',
			url: '/pages_business/pages/reservation/car'
		},
		{
			icon: '/static/icon/meal.png',
			name: '试餐预约',
			url: '/pages_business/pages/reservation/tasting'
		},
		{
			icon: '/static/icon/roomType.png',
			name: '看房预约',
			url: '/pages_business/pages/reservation/room-viewing'
		},
		{
			icon: '/static/icon/more.png',
			name: '用品补充',
			url: '/pages_business/pages/supply/request'
		}
		// 开发环境测试入口
		// ,{
		// 	icon: '/static/icon/more.png',
		// 	name: 'API测试',
		// 	url: '/pages_business/pages/test/api-test'
		// }
	]);
	// 拨打电话方法
	const makePhoneCall = () => {
		uni.makePhoneCall({
			phoneNumber: '***********'
		});
	}

	// 显示微信二维码
	const showWechatQR = () => {
		showQRCode.value = true
	}

	// 关闭微信二维码弹窗
	const closeQRCode = () => {
		showQRCode.value = false;
	}

	// 二维码长按事件处理
	const onQRCodeLongPress = () => {
		// 在微信小程序中，show-menu-by-longpress 属性会自动处理长按识别二维码
		// 这里可以添加一些提示信息或统计
		console.log('用户长按了二维码');
	}

	// 分享给朋友
	const onShareAppMessage = () => {
		return getShareAppMessageConfig({
			title: '东方爱堡（夏雨馨竹）月子会所 - 专业月子护理服务',
			path: '/pages/index/index',
		});
	}

	// 分享到朋友圈
	const onShareTimeline = () => {
		return getShareTimelineConfig({
			title: '东方爱堡（夏雨馨竹）月子会所 - 专业月子护理服务，为您和宝宝提供最贴心的照护',
		});
	}

	onMounted(() => {
		const menuButton = uni.getMenuButtonBoundingClientRect();
		navBarHeight.value = (((menuButton.height - 20) / 2) + menuButton.top) * 2;
	});
</script>
<style lang="scss" scoped>
	.index-container {
		background-color: #FAFAF5;
		min-height: 100vh;
		font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
	}

	/* 顶部宣传横幅样式 */
	.banner-section {
		position: relative;
		width: 100%;
		height: 610rpx;
		overflow: hidden;
	}

	.banner-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.banner-overlay {
		position: fixed;
		left: 24rpx;
		height: 40rpx;
		line-height: 40rpx;
		text-align: left;
	}

	.banner-title {
		font-size: 28rpx;
		color: #ffffff;
		text-align: center;
		text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
	}

	/* 欢迎信息条样式 */
	.welcome-bar {
		display: flex;
		align-items: center;
		background-color: rgba(206, 182, 132, 0.3);
		padding: 16rpx;
		/* 移除底部边框 */
	}

	.welcome-text {
		margin-left: 8rpx;
		font-size: 24rpx;
		color: #695325;

	}

	/* 导航图标样式 */
	.nav-section {
		margin-top: 6rpx;
		padding: 16rpx 20rpx;
	}

	.nav-grid {
		display: flex;
		justify-content: space-between;
		padding: 10rpx 10rpx;
		margin-bottom: 5rpx;
	}

	.nav-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 20%;
	}

	.nav-icon {
		width: 80rpx;
		height: 80rpx;
		border-radius: 20rpx;
		margin-bottom: 10rpx;
		background-color: #CEB684;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.nav-icon image {
		width: 50rpx;
		height: 50rpx;
	}

	.nav-name {
		font-size: 24rpx;
		color: #333;
		text-align: center;
	}

	.divider-line {
		margin: 40rpx 44rpx;
		background-color: #EDE6D3;
		height: 2rpx;
	}

	/* 联系我们区域样式 */
	.contact-section {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 32rpx;
	}

	.contact-header {
		display: flex;
		flex-direction: column;
	}

	.contact-title {
		color: #333;
		font-size: 36rpx;
		font-weight: 600;
	}

	.contact-subtitle {
		font-size: 24rpx;
		color: #666;
		margin-top: 12rpx;
	}

	.contact-buttons {
		display: flex;
		gap: 20rpx;
		margin-top: 36rpx;
	}

	.contact-button {
		width: 200rpx;
		height: 64rpx;
		border-radius: 50rpx;
		background-color: #CEB684;
		font-size: 28rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.contact-button-text {
		color: #fff;
		margin-left: 8rpx;
	}

	/* 分享按钮样式 */
	.share-buttons {
		margin-top: 20rpx;
	}

	.share-button {
		width: 200rpx;
		height: 64rpx;
		border-radius: 50rpx;
		background-color: #8b5a2b;
		font-size: 26rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border: none;
		padding: 0;
		margin: 0;
		line-height: 1;
	}

	.share-button::after {
		border: none;
	}

	.share-button-text {
		color: #fff;
		margin-left: 6rpx;
		font-size: 26rpx;
	}

	/* 微信二维码弹窗样式 */
	.qrcode-popup {
		padding: 40rpx 60rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.qrcode-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 30rpx;
	}

	.qrcode-image {
		width: 400rpx;
		height: 400rpx;
		margin-bottom: 20rpx;
	}

	.qrcode-desc {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 10rpx;
	}

	.qrcode-hint {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 30rpx;
		text-align: center;
	}

	.close-button {
		width: 200rpx;
		height: 70rpx;
		line-height: 70rpx;
		text-align: center;
		background-color: #CEB684;
		color: #fff;
		border-radius: 35rpx;
		font-size: 28rpx;
	}

	.contact-divider {
		height: 1px;
		background-color: #f0f0f0;
		margin: 10rpx 0 30rpx;
		width: 80%;
		margin-left: auto;
		margin-right: auto;
	}

	.contact-right {
		width: 276rpx;
		height: 204rpx;
	}

	.contact-right image {
		width: 100%;
		height: 100%;
	}

	/* 合作商家区域样式 */
	.partners-section {
		padding: 30rpx 32rpx 50rpx;
	}

	.section-header {
		display: flex;
		flex-direction: column;
		margin-bottom: 30rpx;
	}

	.section-title {
		color: #333;
		font-size: 36rpx;
		font-weight: 600;
	}

	.section-subtitle {
		font-size: 24rpx;
		color: #666;
		margin-top: 12rpx;
	}

	.partners-grid {
		display: flex;
		justify-content: flex-start;
		padding-left: 20rpx;
		gap: 30rpx;
	}

	.partner-item {
		width: 300rpx;
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.partner-logo {
		width: 100%;
		height: 120rpx;
		object-fit: contain;
	}
</style>
