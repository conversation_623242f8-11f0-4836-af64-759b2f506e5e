import request from '@/utils/request'

// 查询通用预约列表
export function listUniversalReservation(query) {
  return request({
    url: '/admin/universal-reservations/page',
    method: 'post',
    data: query
  })
}

// 查询通用预约详细
export function getUniversalReservation(data) {
  return request({
    url: '/admin/universal-reservations/get',
    method: 'post',
    data: data
  })
}

// 新增通用预约
export function addUniversalReservation(data) {
  return request({
    url: '/admin/universal-reservations/save',
    method: 'post',
    data: data
  })
}

// 修改通用预约
export function updateUniversalReservation(data) {
  return request({
    url: '/admin/universal-reservations/update',
    method: 'post',
    data: data
  })
}

// 删除通用预约
export function delUniversalReservation(data) {
  return request({
    url: '/admin/universal-reservations/remove',
    method: 'post',
    data: data
  })
}

// 根据预约类型获取预约列表
export function getReservationsByType(data) {
  return request({
    url: '/api/universal-reservations/list-by-type',
    method: 'post',
    data: data
  })
}

// 根据会员入住记录ID获取预约列表
export function getReservationsByMember(data) {
  return request({
    url: '/api/universal-reservations/list-by-member',
    method: 'post',
    data: data
  })
}

// 更新预约状态
export function updateReservationStatus(data) {
  return request({
    url: '/api/universal-reservations/update-status',
    method: 'post',
    data: data
  })
}

// 分配处理人员
export function assignHandler(data) {
  return request({
    url: '/api/universal-reservations/assign-handler',
    method: 'post',
    data: data
  })
}

// 完成预约
export function completeReservation(data) {
  return request({
    url: '/api/universal-reservations/complete',
    method: 'post',
    data: data
  })
}

// 取消预约
export function cancelReservation(data) {
  return request({
    url: '/api/universal-reservations/cancel',
    method: 'post',
    data: data
  })
}

// 获取预约类型列表
export function getReservationTypes() {
  return request({
    url: '/api/universal-reservations/types',
    method: 'post'
  })
}

// 获取预约统计信息
export function getReservationStatistics(data) {
  return request({
    url: '/api/universal-reservations/statistics',
    method: 'post',
    data: data
  })
}
