package com.dfab.handicraftClass.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dfab.handicraftClass.entity.HandicraftClass;
import com.github.pagehelper.PageInfo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 手工课安排服务接口
 */
public interface HandicraftClassService extends IService<HandicraftClass> {
    

    
    /**
     * 根据课程类型获取手工课列表
     * @param classType 课程类型
     * @return 手工课列表
     */
    List<HandicraftClass> getClassesByType(Integer classType);
    
    /**
     * 根据状态获取手工课列表
     * @param status 课程状态
     * @return 手工课列表
     */
    List<HandicraftClass> getClassesByStatus(Integer status);
    
    /**
     * 根据老师获取手工课列表
     * @param teacher 授课老师
     * @return 手工课列表
     */
    List<HandicraftClass> getClassesByTeacher(String teacher);
    
    /**
     * 根据时间范围获取手工课列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 手工课列表
     */
    List<HandicraftClass> getClassesByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
    

    
    /**
     * 后台管理员创建手工课
     * @param handicraftClass 手工课信息
     * @return 保存后的手工课信息
     */
    HandicraftClass createClassByAdmin(HandicraftClass handicraftClass);
    
    /**
     * 分页查询手工课列表（后台管理用）
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<HandicraftClass> getClassesPageByMybatisPlus(int pageNum, int pageSize, HandicraftClass queryParams);
    
    /**
     * 更新课程状态
     * @param id 手工课ID
     * @param status 新状态
     * @return 更新结果
     */
    boolean updateClassStatus(Long id, Integer status);
    
    /**
     * 分配授课老师
     * @param id 手工课ID
     * @param teacher 授课老师
     * @param teacherPhone 老师电话
     * @return 更新结果
     */
    boolean assignTeacher(Long id, String teacher, String teacherPhone);
    
    /**
     * 完成课程并记录实际时间
     * @param id 手工课ID
     * @param actualStartTime 实际开始时间
     * @param actualEndTime 实际结束时间
     * @return 更新结果
     */
    boolean completeClass(Long id, LocalDateTime actualStartTime, LocalDateTime actualEndTime);
    
    /**
     * 取消课程
     * @param id 手工课ID
     * @param cancelReason 取消原因
     * @return 更新结果
     */
    boolean cancelClass(Long id, String cancelReason);
    
    /**
     * 评价课程
     * @param id 手工课ID
     * @param rating 评分
     * @param review 评价内容
     * @return 更新结果
     */
    boolean rateClass(Long id, Integer rating, String review);
    
    /**
     * 参与课程（增加参与人数）
     * @param id 手工课ID
     * @param openId 用户openId
     * @return 参与结果
     */
    boolean joinClass(Long id, String openId);
    
    /**
     * 退出课程（减少参与人数）
     * @param id 手工课ID
     * @param openId 用户openId
     * @return 退出结果
     */
    boolean leaveClass(Long id, String openId);
    
    /**
     * 获取今日课程统计
     * @param classType 课程类型（可选）
     * @return 统计数据
     */
    long getTodayClassCount(Integer classType);
    
    /**
     * 获取待开始课程数量
     * @param classType 课程类型（可选）
     * @return 待开始数量
     */
    long getPendingClassCount(Integer classType);
    
    /**
     * 获取可参与的课程列表（有空余名额的课程）
     * @return 可参与的课程列表
     */
    List<HandicraftClass> getAvailableClasses();

    /**
     * 更新课程的当前参与人数
     * @param classId 课程ID
     * @return 更新结果
     */
    boolean updateCurrentParticipants(Long classId);
}
