package com.dfab.handicraftClass.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dfab.handicraftClass.entity.HandicraftClass;
import com.dfab.handicraftClass.enums.ClassTypeEnum;
import com.dfab.handicraftClass.service.HandicraftClassService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 手工课安排后台管理控制器
 */
@RestController
@RequestMapping("/admin/handicraft-classes")
@RequiredArgsConstructor
@Tag(name = "HandicraftClassController", description = "手工课安排管理接口，提供后台对手工课程的增删改查功能")
public class HandicraftClassController {

    private final HandicraftClassService handicraftClassService;

    /**
     * 分页查询手工课列表
     * @param handicraftClass 查询参数（包含分页信息）
     * @return 分页结果
     */
    @Operation(summary = "分页查询手工课列表", description = "根据查询条件分页获取手工课列表")
    @PostMapping("/page")
    public Page<HandicraftClass> getClassesPage(
            @Parameter(description = "查询参数（包含分页信息）") @RequestBody(required = false) HandicraftClass handicraftClass) {
        if (handicraftClass == null) {
            handicraftClass = new HandicraftClass();
        }
        // 设置默认分页参数
        int pageNum = handicraftClass.getPageNum() != null ? handicraftClass.getPageNum() : 1;
        int pageSize = handicraftClass.getPageSize() != null ? handicraftClass.getPageSize() : 10;

        return handicraftClassService.getClassesPageByMybatisPlus(pageNum, pageSize, handicraftClass);
    }

    /**
     * 根据ID获取手工课详情
     * @param handicraftClass 包含课程ID的对象
     * @return 课程详情
     */
    @Operation(summary = "根据ID获取手工课详情", description = "通过课程ID获取单个手工课的详细信息")
    @PostMapping("/get")
    public ResponseEntity<HandicraftClass> getClassById(
            @Parameter(description = "课程ID", required = true) @RequestBody HandicraftClass handicraftClass) {
        try {
            HandicraftClass result = handicraftClassService.getById(handicraftClass.getId());
            if (result != null) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 创建手工课
     * @param handicraftClass 课程信息
     * @return 创建结果
     */
    @Operation(summary = "创建手工课", description = "创建新的手工课程")
    @PostMapping("/save")
    @Log(title = "手工课管理-新增", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE)
    public ResponseEntity<?> createClass(
            @Parameter(description = "课程信息", required = true) @Valid @RequestBody HandicraftClass handicraftClass) {
        try {
            handicraftClassService.createClassByAdmin(handicraftClass);
            return ResponseEntity.ok(handicraftClass);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 更新手工课
     * @param handicraftClass 课程信息
     * @return 更新结果
     */
    @Operation(summary = "更新手工课", description = "根据ID更新手工课信息")
    @PostMapping("/update")
    @Log(title = "手工课管理-修改", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public ResponseEntity<?> updateClass(
            @Parameter(description = "课程信息", required = true) @Valid @RequestBody HandicraftClass handicraftClass) {
        try {
            handicraftClassService.updateById(handicraftClass);
            return ResponseEntity.ok(handicraftClass);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 删除手工课
     * @param handicraftClass 包含课程ID的对象
     * @return 删除结果
     */
    @Operation(summary = "删除手工课", description = "根据ID删除手工课")
    @PostMapping("/remove")
    @Log(title = "手工课管理-删除", businessType = BusinessType.DELETE, operatorType = OperatorType.MANAGE)
    public ResponseEntity<?> deleteClass(
            @Parameter(description = "课程ID", required = true) @RequestBody HandicraftClass handicraftClass) {
        try {
            handicraftClassService.removeById(handicraftClass.getId());
            return ResponseEntity.ok("删除成功");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 根据课程类型获取手工课列表
     * @param handicraftClass 包含课程类型的对象
     * @return 课程列表
     */
    @Operation(summary = "根据课程类型获取手工课列表", description = "通过课程类型查询对应的手工课列表")
    @PostMapping("/list-by-type")
    public ResponseEntity<List<HandicraftClass>> getClassesByType(
            @Parameter(description = "课程类型", required = true) @RequestBody HandicraftClass handicraftClass) {
        try {
            List<HandicraftClass> classes = handicraftClassService.getClassesByType(handicraftClass.getClassType());
            return ResponseEntity.ok(classes);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }



    /**
     * 更新课程状态
     * @param handicraftClass 包含课程ID和状态的对象
     * @return 更新结果
     */
    @Operation(summary = "更新课程状态", description = "根据ID更新手工课状态")
    @PostMapping("/update-status")
    @Log(title = "手工课管理-更新状态", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public ResponseEntity<?> updateClassStatus(
            @Parameter(description = "课程信息", required = true) @RequestBody HandicraftClass handicraftClass) {
        try {
            boolean result = handicraftClassService.updateClassStatus(handicraftClass.getId(), handicraftClass.getStatus());
            if (result) {
                return ResponseEntity.ok("状态更新成功");
            } else {
                return ResponseEntity.badRequest().body("状态更新失败");
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 分配授课老师
     * @param handicraftClass 包含课程ID和老师信息的对象
     * @return 分配结果
     */
    @Operation(summary = "分配授课老师", description = "为手工课分配授课老师")
    @PostMapping("/assign-teacher")
    public ResponseEntity<?> assignTeacher(
            @Parameter(description = "课程和老师信息", required = true) @RequestBody HandicraftClass handicraftClass) {
        try {
            boolean result = handicraftClassService.assignTeacher(
                    handicraftClass.getId(), 
                    handicraftClass.getTeacher(), 
                    handicraftClass.getTeacherPhone()
            );
            if (result) {
                return ResponseEntity.ok("授课老师分配成功");
            } else {
                return ResponseEntity.badRequest().body("授课老师分配失败");
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 完成课程
     * @param handicraftClass 包含课程ID和实际时间的对象
     * @return 完成结果
     */
    @Operation(summary = "完成课程", description = "标记课程为已完成并记录实际时间")
    @PostMapping("/complete")
    public ResponseEntity<?> completeClass(
            @Parameter(description = "课程和实际时间信息", required = true) @RequestBody HandicraftClass handicraftClass) {
        try {
            boolean result = handicraftClassService.completeClass(
                    handicraftClass.getId(), 
                    handicraftClass.getActualStartTime(), 
                    handicraftClass.getActualEndTime()
            );
            if (result) {
                return ResponseEntity.ok("课程完成成功");
            } else {
                return ResponseEntity.badRequest().body("课程完成失败");
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 取消课程
     * @param handicraftClass 包含课程ID和取消原因的对象
     * @return 取消结果
     */
    @Operation(summary = "取消课程", description = "取消课程并记录取消原因")
    @PostMapping("/cancel")
    public ResponseEntity<?> cancelClass(
            @Parameter(description = "课程和取消原因", required = true) @RequestBody HandicraftClass handicraftClass) {
        try {
            boolean result = handicraftClassService.cancelClass(handicraftClass.getId(), handicraftClass.getCancelReason());
            if (result) {
                return ResponseEntity.ok("课程取消成功");
            } else {
                return ResponseEntity.badRequest().body("课程取消失败");
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 获取课程类型列表
     * @return 课程类型列表
     */
    @Operation(summary = "获取课程类型列表", description = "获取所有支持的课程类型")
    @PostMapping("/types")
    public ResponseEntity<Map<String, Object>> getClassTypes() {
        try {
            Map<String, Object> result = new HashMap<>();
            Map<Integer, String> types = new HashMap<>();
            for (ClassTypeEnum type : ClassTypeEnum.values()) {
                types.put(type.getCode(), type.getName());
            }
            result.put("types", types);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取课程统计信息
     * @param handicraftClass 包含课程类型的对象（可选）
     * @return 统计信息
     */
    @Operation(summary = "获取课程统计信息", description = "获取今日课程数量和待开始课程数量")
    @PostMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getClassStatistics(
            @Parameter(description = "课程类型（可选）") @RequestBody(required = false) HandicraftClass handicraftClass) {
        try {
            Map<String, Object> result = new HashMap<>();
            Integer classType = handicraftClass != null ? handicraftClass.getClassType() : null;
            
            long todayCount = handicraftClassService.getTodayClassCount(classType);
            long pendingCount = handicraftClassService.getPendingClassCount(classType);
            
            result.put("todayCount", todayCount);
            result.put("pendingCount", pendingCount);
            result.put("classType", classType);
            result.put("classTypeName", ClassTypeEnum.getNameByCode(classType));
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取可参与的课程列表
     * @return 可参与的课程列表
     */
    @Operation(summary = "获取可参与的课程列表", description = "获取有空余名额的课程列表")
    @PostMapping("/available")
    public ResponseEntity<List<HandicraftClass>> getAvailableClasses() {
        try {
            List<HandicraftClass> classes = handicraftClassService.getAvailableClasses();
            return ResponseEntity.ok(classes);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
}
