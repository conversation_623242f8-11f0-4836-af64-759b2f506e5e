import request from '@/utils/request'

// 获取预约列表
export const getReservationList = (params) => {
    return request({
        url: `/api/universalReservation/list-by-type`,
        method: 'post',
        data: params
    })
}

// 获取预约详情
export const getReservationById = (params) => {
    return request({
        url: `/api/universalReservation/get`,
        method: 'post',
        data: params
    })
}

// 创建或更新预约
export const saveReservation = (params) => {
    return request({
        url: `/api/universalReservation/save`,
        method: 'post',
        data: params
    })
}

// 试餐预约
export const createTastingReservation = (params) => {
    return request({
        url: `/api/universalReservation/save`,
        method: 'post',
        data: {
            ...params,
            reservationType: 2, // 试餐预约类型
            title: '试餐预约'
        }
    })
}

// 看房预约
export const createRoomViewingReservation = (params) => {
    return request({
        url: `/api/universalReservation/save`,
        method: 'post',
        data: {
            ...params,
            reservationType: 3, // 看房预约类型
            title: '看房预约'
        }
    })
}

// 用车预约
export const createCarReservation = (params) => {
    return request({
        url: `/api/universalReservation/save`,
        method: 'post',
        data: {
            ...params,
            reservationType: 1, // 用车预约类型
            title: '用车预约'
        }
    })
}

// 取消预约
export const cancelReservation = (params) => {
    return request({
        url: `/api/universalReservation/cancel`,
        method: 'post',
        data: params
    })
}

// 删除预约
export const deleteReservation = (params) => {
    return request({
        url: `/api/universalReservation/delete`,
        method: 'post',
        data: params
    })
}

// 获取预约统计
export const getReservationStats = () => {
    return request({
        url: `/api/universalReservation/stats`,
        method: 'post'
    })
}
