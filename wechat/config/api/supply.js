import request from '@/utils/request'

// 获取用品补充需求列表
export const getSupplyRequestList = (params) => {
    return request({
        url: `/api/customerSupplyRequest/list`,
        method: 'post',
        data: params
    })
}

// 获取用品补充需求详情
export const getSupplyRequestById = (params) => {
    return request({
        url: `/api/customerSupplyRequest/get`,
        method: 'post',
        data: params
    })
}

// 提交用品补充需求
export const submitSupplyRequest = (params) => {
    return request({
        url: `/api/customerSupplyRequest/add`,
        method: 'post',
        data: params
    })
}

// 更新用品补充需求
export const updateSupplyRequest = (params) => {
    return request({
        url: `/api/customerSupplyRequest/update`,
        method: 'post',
        data: params
    })
}

// 删除用品补充需求
export const deleteSupplyRequest = (params) => {
    return request({
        url: `/api/customerSupplyRequest/delete`,
        method: 'post',
        data: params
    })
}

// 获取用品补充需求统计
export const getSupplyRequestStats = () => {
    return request({
        url: `/api/customerSupplyRequest/stats`,
        method: 'post'
    })
}
