package com.dfab.customerSupplyRequest.controller;

import com.dfab.customerSupplyRequest.entity.CustomerSupplyRequest;
import com.dfab.customerSupplyRequest.service.CustomerSupplyRequestService;
import com.dfab.memberCheckin.entity.MemberCheckin;
import com.dfab.memberCheckin.service.MemberCheckinService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 客户用品补充需求 Controller 类，提供客户用品补充需求信息的增删改查接口。
 */
@Slf4j
@RestController
@RequestMapping("/api/customerSupplyRequest")
@Tag(name = "CustomerSupplyRequestController", description = "小程序客户用品补充需求接口，提供对客户用品补充需求信息的增删改查功能")
public class CustomerSupplyRequestController {

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private CustomerSupplyRequestService customerSupplyRequestService;

    @Autowired
    private MemberCheckinService memberCheckinService;

    /**
     * 新增客户用品补充需求
     * @param customerSupplyRequest 需求信息
     * @return 操作结果
     */
    @Operation(summary = "新增客户用品补充需求", description = "用户提交新的用品补充需求")
    @PostMapping("/add")
    @Log(title = "客户用品补充需求-新增", businessType = BusinessType.INSERT, operatorType = OperatorType.MOBILE)
    public CustomerSupplyRequest add(@Parameter(description = "需求信息", required = true) @RequestBody CustomerSupplyRequest customerSupplyRequest) {
        String openId = (String) request.getAttribute("openId");

        customerSupplyRequest.setOpenId(openId);
        customerSupplyRequest.setStatus(0); // 默认待处理状态
        customerSupplyRequest.setDeleteStatus(0); // 默认未删除

        // 自动关联当前入住的会员ID
        try {
            List<MemberCheckin> currentMemberCheckins = memberCheckinService.getCurrentMemberCheckinsByOpenId(openId);
            if (currentMemberCheckins != null && !currentMemberCheckins.isEmpty()) {
                MemberCheckin memberCheckin = currentMemberCheckins.get(0);
                customerSupplyRequest.setMemberCheckinId(memberCheckin.getId());

                // 安全地设置用户信息
                if (memberCheckin.getMemberName() != null && !memberCheckin.getMemberName().trim().isEmpty()) {
                    customerSupplyRequest.setUserName(memberCheckin.getMemberName());
                }
                if (memberCheckin.getPhoneNumber() != null && !memberCheckin.getPhoneNumber().trim().isEmpty()) {
                    customerSupplyRequest.setUserPhone(memberCheckin.getPhoneNumber());
                }
            } else {
                log.warn("用户{}当前没有入住记录，无法关联会员ID", openId);
            }
        } catch (Exception e) {
            log.error("查询会员入住信息失败，openId: {}", openId, e);
            // 不阻断流程，继续保存需求
        }

        customerSupplyRequestService.save(customerSupplyRequest);
        return customerSupplyRequest;
    }

    /**
     * 根据openId获取用户的需求列表
     * @return 需求列表
     */
    @Operation(summary = "获取用户需求列表", description = "根据用户openId获取该用户的所有用品补充需求")
    @PostMapping("/list")
    @Log(title = "客户用品补充需求-查询列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public AjaxResult list() {
        try {
            String openId = (String) request.getAttribute("openId");
            if (openId == null || openId.trim().isEmpty()) {
                return AjaxResult.error("用户身份验证失败，请重新登录");
            }

            List<CustomerSupplyRequest> list = customerSupplyRequestService.getByOpenId(openId);
            return AjaxResult.success(list);
        } catch (Exception e) {
            log.error("查询客户用品补充需求列表失败", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID获取需求详情
     * @param customerSupplyRequest 包含ID的对象
     * @return 需求详情
     */
    @Operation(summary = "根据ID获取需求详情", description = "通过需求ID获取单个需求的详细信息")
    @PostMapping("/get")
    @Log(title = "客户用品补充需求-查询详情", businessType = BusinessType.QUERY, operatorType = OperatorType.MOBILE)
    public AjaxResult get(@Parameter(description = "需求ID", required = true) @RequestBody CustomerSupplyRequest customerSupplyRequest) {
        try {
            String openId = (String) request.getAttribute("openId");
            if (openId == null || openId.trim().isEmpty()) {
                return AjaxResult.error("用户身份验证失败，请重新登录");
            }

            if (customerSupplyRequest.getId() == null) {
                return AjaxResult.error("需求ID不能为空");
            }

            // 验证是否为用户自己的数据
            CustomerSupplyRequest existingRequest = customerSupplyRequestService.getById(customerSupplyRequest.getId());
            if (existingRequest == null) {
                return AjaxResult.error("需求记录不存在");
            }

            if (!openId.equals(existingRequest.getOpenId())) {
                return AjaxResult.error("无权限访问");
            }

            return AjaxResult.success(existingRequest);
        } catch (Exception e) {
            log.error("查询客户用品补充需求详情失败", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 删除需求（用户只能删除自己的需求）
     * @param customerSupplyRequest 包含ID的对象
     * @return 操作结果
     */
    @Operation(summary = "删除需求", description = "用户删除自己的用品补充需求")
    @PostMapping("/delete")
    @Log(title = "客户用品补充需求-删除", businessType = BusinessType.DELETE, operatorType = OperatorType.MOBILE)
    public AjaxResult delete(@Parameter(description = "需求ID", required = true) @RequestBody CustomerSupplyRequest customerSupplyRequest) {
        try {
            String openId = (String) request.getAttribute("openId");
            if (openId == null || openId.trim().isEmpty()) {
                return AjaxResult.error("用户身份验证失败，请重新登录");
            }

            if (customerSupplyRequest.getId() == null) {
                return AjaxResult.error("需求ID不能为空");
            }

            // 验证是否为用户自己的数据
            CustomerSupplyRequest existingRequest = customerSupplyRequestService.getById(customerSupplyRequest.getId());
            if (existingRequest == null) {
                return AjaxResult.error("需求记录不存在");
            }

            if (!openId.equals(existingRequest.getOpenId())) {
                log.error("用户尝试删除他人数据，openId: {}, 记录openId: {}", openId, existingRequest.getOpenId());
                return AjaxResult.error("无权限删除");
            }

            // 逻辑删除
            existingRequest.setDeleteStatus(1);
            boolean result = customerSupplyRequestService.updateById(existingRequest);
            return result ? AjaxResult.success("删除成功") : AjaxResult.error("删除失败");
        } catch (Exception e) {
            log.error("删除客户用品补充需求失败", e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }
}
