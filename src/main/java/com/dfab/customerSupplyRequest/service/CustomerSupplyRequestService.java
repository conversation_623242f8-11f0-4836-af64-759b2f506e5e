package com.dfab.customerSupplyRequest.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dfab.customerSupplyRequest.entity.CustomerSupplyRequest;

import java.util.List;

/**
 * 客户用品补充需求 Service 接口
 */
public interface CustomerSupplyRequestService extends IService<CustomerSupplyRequest> {

    /**
     * 根据openId获取用户的需求列表
     * @param openId 用户openId
     * @return 需求列表
     */
    List<CustomerSupplyRequest> getByOpenId(String openId);

    /**
     * 分页查询客户用品补充需求
     * @param page 分页参数
     * @param customerSupplyRequest 查询条件
     * @return 分页结果
     */
    Page<CustomerSupplyRequest> pageQuery(Page<CustomerSupplyRequest> page, CustomerSupplyRequest customerSupplyRequest);

    /**
     * 分页查询客户用品补充需求（MyBatis-Plus方式）
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<CustomerSupplyRequest> getSupplyRequestsPageByMybatisPlus(int pageNum, int pageSize, CustomerSupplyRequest queryParams);

    /**
     * 处理客户用品补充需求
     * @param id 需求ID
     * @param status 处理状态
     * @param adminRemark 管理员备注
     * @param processBy 处理人
     * @return 是否处理成功
     */
    boolean processRequest(Long id, Integer status, String adminRemark, String processBy);
}
