import request from '@/utils/request'

// 获取手工课列表
export const getHandicraftClassList = (params) => {
    return request({
        url: `/api/handicraftClass/list`,
        method: 'post',
        data: params
    })
}

// 获取手工课详情
export const getHandicraftClassById = (params) => {
    return request({
        url: `/api/handicraftClass/get`,
        method: 'post',
        data: params
    })
}

// 参与手工课
export const joinHandicraftClass = (params) => {
    return request({
        url: `/api/handicraftClass/join`,
        method: 'post',
        data: params
    })
}

// 取消参与手工课
export const cancelHandicraftClass = (params) => {
    return request({
        url: `/api/handicraftClass/cancel`,
        method: 'post',
        data: params
    })
}

// 获取我的手工课参与记录
export const getMyHandicraftClasses = (params) => {
    return request({
        url: `/api/handicraftClass/my-classes`,
        method: 'post',
        data: params
    })
}

// 评价手工课
export const rateHandicraftClass = (params) => {
    return request({
        url: `/api/handicraftClass/rate`,
        method: 'post',
        data: params
    })
}

// 获取手工课统计
export const getHandicraftClassStats = () => {
    return request({
        url: `/api/handicraftClass/stats`,
        method: 'post'
    })
}
