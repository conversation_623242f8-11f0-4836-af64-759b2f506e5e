package com.dfab.customerSupplyRequest.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dfab.customerSupplyRequest.entity.CustomerSupplyRequest;
import com.dfab.customerSupplyRequest.service.CustomerSupplyRequestService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OperatorType;
import com.ruoyi.common.utils.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 客户用品补充需求后台管理 Controller 类，提供客户用品补充需求信息的增删改查接口。
 */
@Slf4j
@RestController
@RequestMapping("/admin/customerSupplyRequest")
@Tag(name = "CustomerSupplyRequestAdminController", description = "客户用品补充需求后台管理接口，提供对客户用品补充需求信息的增删改查功能")
public class CustomerSupplyRequestAdminController {

    @Autowired
    private CustomerSupplyRequestService customerSupplyRequestService;

    /**
     * 分页查询客户用品补充需求列表
     * @param customerSupplyRequest 查询条件
     * @return 分页结果
     */
    @Operation(summary = "分页查询客户用品补充需求列表", description = "根据条件分页查询客户用品补充需求信息")
    @PostMapping("/page")
    @Log(title = "客户用品补充需求后台管理-分页查询", businessType = BusinessType.QUERY, operatorType = OperatorType.MANAGE)
    public AjaxResult page(@Parameter(description = "查询条件和分页参数", required = true) @RequestBody CustomerSupplyRequest customerSupplyRequest) {
        try {
            if (customerSupplyRequest == null) {
                customerSupplyRequest = new CustomerSupplyRequest();
            }

            // 获取分页参数，默认值
            int pageNum = customerSupplyRequest.getPageNum() != null ? customerSupplyRequest.getPageNum() : 1;
            int pageSize = customerSupplyRequest.getPageSize() != null ? customerSupplyRequest.getPageSize() : 10;

            // 限制分页大小，防止查询过多数据
            if (pageSize > 100) {
                pageSize = 100;
            }
            if (pageNum < 1) {
                pageNum = 1;
            }

            Page<CustomerSupplyRequest> result = customerSupplyRequestService.getSupplyRequestsPageByMybatisPlus(pageNum, pageSize, customerSupplyRequest);

            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("分页查询客户用品补充需求失败", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询所有客户用品补充需求列表
     * @param customerSupplyRequest 查询条件
     * @return 需求列表
     */
    @Operation(summary = "查询客户用品补充需求列表", description = "根据条件查询客户用品补充需求信息列表")
    @PostMapping("/list")
    @Log(title = "客户用品补充需求后台管理-查询列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MANAGE)
    public AjaxResult list(@Parameter(description = "查询条件", required = true) @RequestBody CustomerSupplyRequest customerSupplyRequest) {
        try {
            if (customerSupplyRequest == null) {
                customerSupplyRequest = new CustomerSupplyRequest();
            }

            // 限制最大查询数量，防止性能问题
            Page<CustomerSupplyRequest> page = new Page<>(1, 1000);
            Page<CustomerSupplyRequest> result = customerSupplyRequestService.pageQuery(page, customerSupplyRequest);
            return AjaxResult.success(result.getRecords());
        } catch (Exception e) {
            log.error("查询客户用品补充需求列表失败", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID获取客户用品补充需求详情
     * @param customerSupplyRequest 包含ID的对象
     * @return 需求详情
     */
    @Operation(summary = "根据ID获取客户用品补充需求详情", description = "通过需求ID获取单个需求的详细信息")
    @PostMapping("/getById")
    @Log(title = "客户用品补充需求后台管理-查询详情", businessType = BusinessType.QUERY, operatorType = OperatorType.MANAGE)
    public AjaxResult getById(@Parameter(description = "需求ID", required = true) @RequestBody CustomerSupplyRequest customerSupplyRequest) {
        try {
            if (customerSupplyRequest == null || customerSupplyRequest.getId() == null) {
                return AjaxResult.error("需求ID不能为空");
            }

            CustomerSupplyRequest result = customerSupplyRequestService.getById(customerSupplyRequest.getId());
            if (result == null) {
                return AjaxResult.error("需求记录不存在");
            }

            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("查询客户用品补充需求详情失败", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 处理客户用品补充需求
     * @param customerSupplyRequest 需求信息（包含处理状态、管理员备注等）
     * @return 操作结果
     */
    @Operation(summary = "处理客户用品补充需求", description = "管理员处理客户用品补充需求，更新状态和备注")
    @PostMapping("/process")
    @Log(title = "客户用品补充需求后台管理-处理需求", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public AjaxResult process(@Parameter(description = "需求处理信息", required = true) @RequestBody CustomerSupplyRequest customerSupplyRequest) {
        try {
            if (customerSupplyRequest.getId() == null) {
                return AjaxResult.error("需求ID不能为空");
            }
            
            if (customerSupplyRequest.getStatus() == null) {
                return AjaxResult.error("处理状态不能为空");
            }
            
            // 自动获取当前登录用户作为处理人
            String processBy = getCurrentLoginUser();

            boolean result = customerSupplyRequestService.processRequest(
                customerSupplyRequest.getId(),
                customerSupplyRequest.getStatus(),
                customerSupplyRequest.getAdminRemark(),
                processBy
            );
            
            return result ? AjaxResult.success("处理成功") : AjaxResult.error("处理失败");
        } catch (Exception e) {
            log.error("处理客户用品补充需求失败", e);
            return AjaxResult.error("处理失败：" + e.getMessage());
        }
    }

    /**
     * 更新客户用品补充需求
     * @param customerSupplyRequest 需求信息
     * @return 操作结果
     */
    @Operation(summary = "更新客户用品补充需求", description = "管理员更新客户用品补充需求信息")
    @PostMapping("/update")
    @Log(title = "客户用品补充需求后台管理-更新", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    public AjaxResult update(@Parameter(description = "需求信息", required = true) @RequestBody CustomerSupplyRequest customerSupplyRequest) {
        try {
            if (customerSupplyRequest.getId() == null) {
                return AjaxResult.error("需求ID不能为空");
            }
            
            boolean result = customerSupplyRequestService.updateById(customerSupplyRequest);
            return result ? AjaxResult.success("更新成功") : AjaxResult.error("更新失败");
        } catch (Exception e) {
            log.error("更新客户用品补充需求失败", e);
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前登录用户
     * @return 当前登录用户名
     */
    private String getCurrentLoginUser() {
        try {
            return SecurityUtils.getLoginUser().getUser().getNickName();
        } catch (Exception e) {
            log.warn("获取当前登录用户失败: {}", e.getMessage());
            return "系统";
        }
    }
}
